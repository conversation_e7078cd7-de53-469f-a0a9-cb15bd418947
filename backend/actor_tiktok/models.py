from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from cryptography.fernet import Fernet
from django.conf import settings
import json

class TikTokUserAccount(models.Model):
    """Model to store TikTok user authentication data"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tiktok_accounts', null=True, blank=True)
    tiktok_username = models.CharField(max_length=255, help_text="TikTok username")
    tiktok_user_id = models.Char<PERSON>ield(max_length=255, null=True, blank=True, help_text="TikTok user ID")
    email = models.EmailField(null=True, blank=True, help_text="Email for TikTok account")
    password = models.CharField(max_length=255, default='', help_text="Encrypted password for TikTok account")
    encrypted_session_data = models.TextField(help_text="Encrypted TikTok session data")
    is_active = models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=True, help_text="Whether this account is active")
    last_login = models.DateTimeField(null=True, blank=True, help_text="Last successful login to TikTok")
    session_expires_at = models.DateTimeField(null=True, blank=True, help_text="When the session expires")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Anti-bot detection fields
    login_attempts = models.IntegerField(default=0, help_text="Number of login attempts")
    last_attempt_at = models.DateTimeField(null=True, blank=True, help_text="Last login attempt")
    is_blocked = models.BooleanField(default=False, help_text="Whether account is temporarily blocked")
    blocked_until = models.DateTimeField(null=True, blank=True, help_text="When the block expires")
    
    class Meta:
        unique_together = ['user', 'tiktok_username']
        verbose_name = "TikTok User Account"
        verbose_name_plural = "TikTok User Accounts"
    
    def __str__(self):
        return f"{self.user.username} - @{self.tiktok_username}"
    
    def encrypt_password(self, plain_password):
        """Encrypt and store password"""
        if hasattr(settings, 'TIKTOK_ENCRYPTION_KEY'):
            fernet = Fernet(settings.TIKTOK_ENCRYPTION_KEY.encode())
            encrypted_password = fernet.encrypt(plain_password.encode())
            self.password = encrypted_password.decode()
        else:
            # Fallback - store as base64 (not recommended for production)
            import base64
            self.password = base64.b64encode(plain_password.encode()).decode()
    
    def decrypt_password(self):
        """Decrypt and return password"""
        try:
            if hasattr(settings, 'TIKTOK_ENCRYPTION_KEY'):
                fernet = Fernet(settings.TIKTOK_ENCRYPTION_KEY.encode())
                decrypted_password = fernet.decrypt(self.password.encode())
                return decrypted_password.decode()
            else:
                import base64
                return base64.b64decode(self.password.encode()).decode()
        except Exception:
            return ""
    
    def encrypt_session_data(self, session_data):
        """Encrypt and store session data"""
        if hasattr(settings, 'TIKTOK_ENCRYPTION_KEY'):
            fernet = Fernet(settings.TIKTOK_ENCRYPTION_KEY.encode())
            encrypted_data = fernet.encrypt(json.dumps(session_data).encode())
            self.encrypted_session_data = encrypted_data.decode()
        else:
            # Fallback - store as JSON (not recommended for production)
            self.encrypted_session_data = json.dumps(session_data)
    
    def decrypt_session_data(self):
        """Decrypt and return session data"""
        try:
            if hasattr(settings, 'TIKTOK_ENCRYPTION_KEY'):
                fernet = Fernet(settings.TIKTOK_ENCRYPTION_KEY.encode())
                decrypted_data = fernet.decrypt(self.encrypted_session_data.encode())
                return json.loads(decrypted_data.decode())
            else:
                return json.loads(self.encrypted_session_data)
        except Exception:
            return {}
    
    def is_session_valid(self):
        """Check if the session is still valid"""
        if not self.is_active or self.is_blocked:
            return False
        if self.session_expires_at and self.session_expires_at < timezone.now():
            return False
        if self.blocked_until and self.blocked_until > timezone.now():
            return False
        return True
    
    def increment_login_attempts(self):
        """Increment login attempts and block if necessary"""
        self.login_attempts += 1
        self.last_attempt_at = timezone.now()
        
        # Block after 5 failed attempts for 1 hour
        if self.login_attempts >= 5:
            self.is_blocked = True
            self.blocked_until = timezone.now() + timezone.timedelta(hours=1)
        
        self.save()
    
    def reset_login_attempts(self):
        """Reset login attempts after successful login"""
        self.login_attempts = 0
        self.is_blocked = False
        self.blocked_until = None
        self.last_login = timezone.now()
        self.save()

class ActorTask(models.Model):
    """Model for actor-specific TikTok scraping tasks"""
    TASK_TYPE_CHOICES = [
        ('MY_VIDEOS', 'My Videos'),
        ('MY_FOLLOWERS', 'My Followers'),
        ('MY_FOLLOWING', 'My Following'),
        ('MY_LIKES', 'My Liked Videos'),
        ('FEED_SCRAPE', 'Feed Scraping'),
        ('TARGETED_USER', 'Targeted User Analysis'),
        ('HASHTAG_ANALYSIS', 'Hashtag Analysis'),
        ('COMPETITOR_ANALYSIS', 'Competitor Analysis'),
        ('CONTENT_SEARCH', 'Content Search'),
    ]
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('RUNNING', 'Running'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
        ('PAUSED', 'Paused'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='actor_tasks')
    tiktok_account = models.ForeignKey(TikTokUserAccount, on_delete=models.CASCADE, related_name='tasks')
    task_name = models.CharField(max_length=255, help_text="Name of the scraping task")
    task_type = models.CharField(max_length=20, choices=TASK_TYPE_CHOICES, help_text="Type of actor task")
    target_identifier = models.CharField(max_length=255, null=True, blank=True, help_text="Target username, hashtag, or URL")
    keywords = models.TextField(null=True, blank=True, help_text="Keywords for content search (comma-separated)")
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='PENDING')
    
    # Task configuration
    max_items = models.IntegerField(default=100, help_text="Maximum items to scrape")
    scrape_interval = models.IntegerField(default=60, help_text="Interval between requests (seconds)")
    use_stealth_mode = models.BooleanField(default=True, help_text="Use anti-detection measures")
    randomize_delays = models.BooleanField(default=True, help_text="Randomize request delays")
    
    # Date range
    start_date = models.DateField(null=True, blank=True, help_text="Start date for data collection")
    end_date = models.DateField(null=True, blank=True, help_text="End date for data collection")
    
    # Task metadata
    task_parameters = models.JSONField(default=dict, blank=True, help_text="JSON parameters for the task")
    celery_task_id = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)
    
    # Progress tracking
    items_scraped = models.IntegerField(default=0, help_text="Number of items successfully scraped")
    total_items_found = models.IntegerField(default=0, help_text="Total items found")
    progress_percentage = models.FloatField(default=0.0, help_text="Task completion percentage")
    
    class Meta:
        verbose_name = "Actor Task"
        verbose_name_plural = "Actor Tasks"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.task_name} - {self.task_type} ({self.status})"
    
    def update_progress(self, items_scraped, total_items=None):
        """Update task progress"""
        self.items_scraped = items_scraped
        if total_items:
            self.total_items_found = total_items
        
        if self.total_items_found > 0:
            self.progress_percentage = (self.items_scraped / self.total_items_found) * 100
        else:
            self.progress_percentage = 0.0
        
        self.save()

    def get_scraped_videos(self):
        """Get all scraped video data for this task"""
        return self.actorscrapeddata_set.filter(data_type='VIDEO')

    def get_content_stats(self):
        """Get content statistics for this task"""
        videos = self.get_scraped_videos()
        if not videos.exists():
            return {
                'total_videos': 0,
                'total_likes': 0,
                'total_comments': 0,
                'total_shares': 0,
                'total_views': 0,
                'unique_authors': 0
            }

        total_likes = 0
        total_comments = 0
        total_shares = 0
        total_views = 0
        unique_authors = set()

        for video in videos:
            content = video.content
            total_likes += content.get('likes', 0)
            total_comments += content.get('comments', 0)
            total_shares += content.get('shares', 0)
            total_views += content.get('views', 0)

            if content.get('author'):
                unique_authors.add(content['author'])

        return {
            'total_videos': videos.count(),
            'total_likes': total_likes,
            'total_comments': total_comments,
            'total_shares': total_shares,
            'total_views': total_views,
            'unique_authors': len(unique_authors),
            'avg_likes': total_likes / videos.count() if videos.count() > 0 else 0,
            'avg_comments': total_comments / videos.count() if videos.count() > 0 else 0,
            'avg_shares': total_shares / videos.count() if videos.count() > 0 else 0,
            'avg_views': total_views / videos.count() if videos.count() > 0 else 0
        }

class ActorScrapedData(models.Model):
    """Model to store scraped data from actor tasks"""
    DATA_TYPE_CHOICES = [
        ('VIDEO', 'Video Data'),
        ('USER', 'User Data'),
        ('FOLLOWER', 'Follower Data'),
        ('FOLLOWING', 'Following Data'),
        ('LIKE', 'Liked Video Data'),
        ('FEED_ITEM', 'Feed Item Data'),
        ('HASHTAG_DATA', 'Hashtag Data'),
        ('ANALYTICS', 'Analytics Data'),
    ]
    
    task = models.ForeignKey(ActorTask, on_delete=models.CASCADE, related_name='scraped_data')
    data_type = models.CharField(max_length=20, choices=DATA_TYPE_CHOICES)
    content = models.JSONField(help_text="JSON content of the scraped data")
    tiktok_id = models.CharField(max_length=255, null=True, blank=True, help_text="TikTok ID of the content")
    scraped_at = models.DateTimeField(auto_now_add=True)
    
    # Data quality metrics
    is_complete = models.BooleanField(default=True, help_text="Whether all expected data was scraped")
    quality_score = models.FloatField(default=1.0, help_text="Data quality score (0.0 to 1.0)")
    
    class Meta:
        verbose_name = "Actor Scraped Data"
        verbose_name_plural = "Actor Scraped Data"
        ordering = ['-scraped_at']
        indexes = [
            models.Index(fields=['task', 'data_type']),
            models.Index(fields=['tiktok_id']),
            models.Index(fields=['scraped_at']),
        ]
    
    def __str__(self):
        return f"{self.data_type} for Task {self.task.id} at {self.scraped_at.strftime('%Y-%m-%d %H:%M:%S')}"

class TikTokSession(models.Model):
    """Model to track TikTok session health and anti-bot measures"""
    tiktok_account = models.ForeignKey(TikTokUserAccount, on_delete=models.CASCADE, related_name='sessions')
    session_id = models.CharField(max_length=255, unique=True)
    user_agent = models.TextField(help_text="User agent used for this session")
    proxy_used = models.CharField(max_length=255, null=True, blank=True, help_text="Proxy used for this session")
    
    # Session health metrics
    requests_made = models.IntegerField(default=0, help_text="Number of requests made in this session")
    successful_requests = models.IntegerField(default=0, help_text="Number of successful requests")
    failed_requests = models.IntegerField(default=0, help_text="Number of failed requests")
    captcha_challenges = models.IntegerField(default=0, help_text="Number of CAPTCHA challenges encountered")
    rate_limit_hits = models.IntegerField(default=0, help_text="Number of rate limit hits")
    
    # Anti-detection metrics
    detection_score = models.FloatField(default=0.0, help_text="Bot detection risk score (0.0 to 1.0)")
    last_activity = models.DateTimeField(auto_now=True)
    is_healthy = models.BooleanField(default=True, help_text="Whether session is healthy")
    
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(help_text="When this session expires")
    
    class Meta:
        verbose_name = "TikTok Session"
        verbose_name_plural = "TikTok Sessions"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Session {self.session_id} for {self.tiktok_account.tiktok_username}"
    
    def update_health_metrics(self, success=True, captcha=False, rate_limited=False):
        """Update session health metrics"""
        self.requests_made += 1
        
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        
        if captcha:
            self.captcha_challenges += 1
        
        if rate_limited:
            self.rate_limit_hits += 1
        
        # Calculate detection score
        if self.requests_made > 0:
            failure_rate = self.failed_requests / self.requests_made
            captcha_rate = self.captcha_challenges / self.requests_made
            rate_limit_rate = self.rate_limit_hits / self.requests_made
            
            self.detection_score = min(1.0, (failure_rate * 0.3) + (captcha_rate * 0.5) + (rate_limit_rate * 0.2))
            self.is_healthy = self.detection_score < 0.3
        
        self.save()
