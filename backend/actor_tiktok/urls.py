from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    TikTokUserAccountViewSet, TikTokLoginView, ActorTaskViewSet,
    ActorScrapedDataViewSet, TikTokSessionViewSet, HealthCheckView,
    MultiAccountLoginView
)

# Create router for ViewSets
router = DefaultRouter()
router.register(r'accounts', TikTokUserAccountViewSet, basename='tiktok-accounts')
router.register(r'tasks', ActorTaskViewSet, basename='actor-tasks')
router.register(r'scraped-data', ActorScrapedDataViewSet, basename='actor-scraped-data')
router.register(r'sessions', TikTokSessionViewSet, basename='tiktok-sessions')

urlpatterns = [
    # API endpoints
    path('api/actor/', include(router.urls)),
    
    # Authentication endpoints
    path('api/actor/login/', TikTokLoginView.as_view(), name='tiktok-login'),
    path('api/actor/multi-login/', MultiAccountLoginView.as_view(), name='multi-account-login'),
    
    # Health check
    path('api/actor/health/', HealthCheckView.as_view(), name='actor-health'),
]