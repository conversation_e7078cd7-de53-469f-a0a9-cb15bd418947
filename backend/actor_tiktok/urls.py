from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    simple_login_test, scrape_prabowo_content,
    prabowo_content_stats, system_status, scrape_content_by_keyword,
    get_search_history, save_search_preset
)

urlpatterns = [

    # Simple login endpoints (Working version)
    path('api/actor/simple-login/', simple_login_test, name='simple-login-test'),
    path('api/actor/scrape-prabowo/', scrape_prabowo_content, name='scrape-prabowo'),
    path('api/actor/scrape-keyword/', scrape_content_by_keyword, name='scrape-keyword'),
    path('api/actor/search-history/', get_search_history, name='search-history'),
    path('api/actor/search-presets/', save_search_preset, name='search-presets'),
    path('api/actor/prabowo-stats/', prabowo_content_stats, name='prabowo-stats'),
    path('api/actor/system-status/', system_status, name='system-status'),


]