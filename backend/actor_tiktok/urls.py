from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    simple_login_test, scrape_prabowo_content,
    prabowo_content_stats, system_status, scrape_content_by_keyword,
    get_search_history, get_search_presets,
    get_health_status, get_accounts, get_tasks, get_task_statistics,
    get_sessions, get_scraped_data, get_scraped_data_by_task,
    get_active_sessions
)

urlpatterns = [
    # Health and system endpoints
    path('api/actor/health/', get_health_status, name='health-status'),
    path('api/actor/system-status/', system_status, name='system-status'),

    # Account management
    path('api/actor/accounts/', get_accounts, name='accounts'),

    # Task management
    path('api/actor/tasks/', get_tasks, name='tasks'),
    path('api/actor/tasks/stats/', get_task_statistics, name='task-statistics'),

    # Session management
    path('api/actor/sessions/', get_sessions, name='sessions'),
    path('api/actor/active-sessions/', get_active_sessions, name='active-sessions'),

    # Scraped data management
    path('api/actor/scraped-data/', get_scraped_data, name='scraped-data'),
    path('api/actor/scraped-data/by_task/', get_scraped_data_by_task, name='scraped-data-by-task'),

    # Search functionality
    path('api/actor/search-history/', get_search_history, name='search-history'),
    path('api/actor/search-presets/', get_search_presets, name='get-search-presets'),

    # Content scraping endpoints
    path('api/actor/simple-login/', simple_login_test, name='simple-login-test'),
    path('api/actor/scrape-prabowo/', scrape_prabowo_content, name='scrape-prabowo'),
    path('api/actor/scrape-keyword/', scrape_content_by_keyword, name='scrape-keyword'),
    path('api/actor/prabowo-stats/', prabowo_content_stats, name='prabowo-stats'),
]