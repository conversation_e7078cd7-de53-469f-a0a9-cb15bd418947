from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import (
    TikTokUserAccountViewSet, TikTokLoginView, ActorTaskViewSet,
    ActorScrapedDataViewSet, TikTokSessionViewSet, HealthCheckView,
    MultiAccountLoginView, simple_login_test, scrape_prabowo_content,
    prabowo_content_stats, system_status, scrape_content_by_keyword,
    get_search_history, save_search_preset
)

# Create router for ViewSets
router = DefaultRouter()
router.register(r'accounts', TikTokUserAccountViewSet, basename='tiktok-accounts')
router.register(r'tasks', ActorTaskViewSet, basename='actor-tasks')
router.register(r'scraped-data', ActorScrapedDataViewSet, basename='actor-scraped-data')
router.register(r'sessions', TikTokSessionViewSet, basename='tiktok-sessions')

urlpatterns = [
    # API endpoints
    path('api/actor/', include(router.urls)),

    # Authentication endpoints
    path('api/actor/login/', TikTokLoginView.as_view(), name='tiktok-login'),
    path('api/actor/multi-login/', MultiAccountLoginView.as_view(), name='multi-account-login'),

    # Simple login endpoints (Working version)
    path('api/actor/simple-login/', simple_login_test, name='simple-login-test'),
    path('api/actor/scrape-prabowo/', scrape_prabowo_content, name='scrape-prabowo'),
    path('api/actor/scrape-keyword/', scrape_content_by_keyword, name='scrape-keyword'),
    path('api/actor/search-history/', get_search_history, name='search-history'),
    path('api/actor/search-presets/', save_search_preset, name='search-presets'),
    path('api/actor/prabowo-stats/', prabowo_content_stats, name='prabowo-stats'),
    path('api/actor/system-status/', system_status, name='system-status'),

    # Health check
    path('api/actor/health/', HealthCheckView.as_view(), name='actor-health'),
]