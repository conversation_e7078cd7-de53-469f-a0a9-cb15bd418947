import time
import random
import logging
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from fake_useragent import UserAgent

logger = logging.getLogger(__name__)

class AntiDetectionManager:
    """
    Manages anti-detection techniques for web scraping
    """
    
    def __init__(self):
        self.ua = UserAgent()
        self.viewport_sizes = [
            (1920, 1080), (1366, 768), (1440, 900), (1536, 864),
            (1280, 720), (1600, 900), (1024, 768), (1280, 1024)
        ]
        
        # Common browser fingerprint data
        self.languages = ['en-US', 'en-GB', 'en-CA', 'en-AU']
        self.timezones = [
            'America/New_York', 'America/Los_Angeles', 'America/Chicago',
            'Europe/London', 'Europe/Berlin', 'Asia/Tokyo'
        ]
    
    def setup_driver(self, headless=False, proxy=None):
        """
        Setup Chrome driver with anti-detection measures
        
        Args:
            headless: Whether to run in headless mode
            proxy: Proxy configuration if needed
        
        Returns:
            WebDriver instance
        """
        try:
            options = Options()
            
            # Basic anti-detection options
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option('excludeSwitches', ['enable-automation'])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Disable images and CSS for faster loading (optional)
            # options.add_argument('--disable-images')
            # options.add_argument('--disable-css')
            
            # Random user agent
            user_agent = self.ua.random
            options.add_argument(f'--user-agent={user_agent}')
            
            # Random viewport size
            viewport = random.choice(self.viewport_sizes)
            options.add_argument(f'--window-size={viewport[0]},{viewport[1]}')
            
            # Language and locale
            language = random.choice(self.languages)
            options.add_argument(f'--lang={language}')
            options.add_argument(f'--accept-lang={language}')
            
            # Disable notifications and enable logging
            prefs = {
                'profile.default_content_setting_values.notifications': 2,
                'profile.default_content_settings.popups': 0,
                'profile.managed_default_content_settings.images': 2
            }
            options.add_experimental_option('prefs', prefs)
            
            # Enable browser logging to capture JavaScript errors
            options.set_capability('goog:loggingPrefs', {
                'browser': 'ALL',
                'driver': 'ALL',
                'performance': 'ALL'
            })
            
            # Proxy configuration
            if proxy:
                options.add_argument(f'--proxy-server={proxy}')
            
            # Headless mode
            if headless:
                options.add_argument('--headless')
            
            # Enhanced Chrome options for maximum stealth
            options.add_argument('--disable-web-security')
            options.add_argument('--allow-running-insecure-content')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-gpu')
            # Note: --disable-logging removed to enable JavaScript error capture
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-ipc-flooding-protection')
            options.add_argument('--disable-hang-monitor')
            options.add_argument('--disable-client-side-phishing-detection')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--disable-prompt-on-repost')
            options.add_argument('--disable-domain-reliability')
            options.add_argument('--disable-component-extensions-with-background-pages')
            options.add_argument('--disable-background-networking')
            options.add_argument('--disable-breakpad')
            options.add_argument('--disable-component-update')
            options.add_argument('--disable-features=AudioServiceOutOfProcess')
            options.add_argument('--disable-features=MediaRouter')
            options.add_argument('--disable-features=Translate')
            
            # Create driver
            driver = webdriver.Chrome(options=options)
            
            # Execute enhanced stealth scripts
            self._execute_enhanced_stealth_scripts(driver)
            
            # Set random timezone
            timezone = random.choice(self.timezones)
            driver.execute_cdp_cmd('Emulation.setTimezoneOverride', {'timezoneId': timezone})
            
            # Set random geolocation
            self._set_random_geolocation(driver)
            
            logger.info(f"Driver setup complete with user agent: {user_agent}")
            return driver
        
        except Exception as e:
            logger.error(f"Error setting up driver: {str(e)}")
            raise
    
    def _execute_enhanced_stealth_scripts(self, driver):
        """
        Execute enhanced JavaScript to make the browser appear more human-like
        """
        try:
            # Execute comprehensive stealth script
            stealth_script = """
            // Hide webdriver property completely
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
                configurable: true
            });
            
            // Remove webdriver from window
            delete window.webdriver;
            
            // Override automation indicators
            Object.defineProperty(window, 'callPhantom', { value: undefined });
            Object.defineProperty(window, '_phantom', { value: undefined });
            Object.defineProperty(window, 'phantom', { value: undefined });
            Object.defineProperty(window, '__nightmare', { value: undefined });
            Object.defineProperty(window, '_selenium', { value: undefined });
            Object.defineProperty(window, '__selenium_unwrapped', { value: undefined });
            Object.defineProperty(window, '__selenium_evaluate', { value: undefined });
            Object.defineProperty(window, '__fxdriver_evaluate', { value: undefined });
            Object.defineProperty(window, '__driver_unwrapped', { value: undefined });
            Object.defineProperty(window, '__webdriver_unwrapped', { value: undefined });
            Object.defineProperty(window, '__driver_evaluate', { value: undefined });
            Object.defineProperty(window, '__webdriver_evaluate', { value: undefined });
            Object.defineProperty(window, '__fxdriver_unwrapped', { value: undefined });
            
            // Override plugins with realistic data
            Object.defineProperty(navigator, 'plugins', {
                get: () => {
                    return {
                        0: { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer' },
                        1: { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai' },
                        2: { name: 'Native Client', filename: 'internal-nacl-plugin' },
                        length: 3
                    };
                },
                configurable: true
            });
            
            // Override languages with more realistic data
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
                configurable: true
            });
            
            // Override chrome property with realistic structure
            if (!window.chrome) {
                window.chrome = {};
            }
            window.chrome.runtime = {
                onConnect: undefined,
                onMessage: undefined
            };
            
            // Override permissions API
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => {
                if (parameters.name === 'notifications') {
                    return Promise.resolve({ state: Notification.permission });
                }
                return originalQuery(parameters);
            };
            
            // Override screen properties to match common resolutions
            Object.defineProperty(screen, 'availHeight', { value: screen.height - 40 });
            Object.defineProperty(screen, 'availWidth', { value: screen.width });
            
            // Add realistic timing variations
            const originalSetTimeout = window.setTimeout;
            window.setTimeout = function(callback, delay) {
                const jitter = Math.random() * 2 - 1; // -1 to 1ms jitter
                return originalSetTimeout(callback, delay + jitter);
            };
            
            // Override Date.now to add slight timing variations
            const originalNow = Date.now;
            Date.now = function() {
                return originalNow() + Math.floor(Math.random() * 2);
            };
            
            // Hide automation traces in console
            const originalLog = console.log;
            console.log = function(...args) {
                if (args.some(arg => typeof arg === 'string' && 
                    (arg.includes('webdriver') || arg.includes('automation')))) {
                    return;
                }
                return originalLog.apply(console, args);
            };
            """
            
            driver.execute_script(stealth_script)
            
            logger.info("Enhanced stealth scripts executed successfully")
        
        except Exception as e:
            logger.warning(f"Error executing enhanced stealth scripts: {str(e)}")
    
    def _set_random_geolocation(self, driver):
        """
        Set a random geolocation to appear more natural
        """
        try:
            # Random coordinates (major cities)
            locations = [
                {'latitude': 40.7128, 'longitude': -74.0060, 'accuracy': 100},  # New York
                {'latitude': 34.0522, 'longitude': -118.2437, 'accuracy': 100}, # Los Angeles
                {'latitude': 51.5074, 'longitude': -0.1278, 'accuracy': 100},   # London
                {'latitude': 48.8566, 'longitude': 2.3522, 'accuracy': 100},    # Paris
                {'latitude': 35.6762, 'longitude': 139.6503, 'accuracy': 100},  # Tokyo
            ]
            
            location = random.choice(locations)
            driver.execute_cdp_cmd('Emulation.setGeolocationOverride', location)
            
            logger.info(f"Set geolocation to: {location['latitude']}, {location['longitude']}")
        
        except Exception as e:
            logger.warning(f"Error setting geolocation: {str(e)}")
    
    def randomize_viewport(self, driver):
        """
        Randomize viewport size during session
        """
        try:
            viewport = random.choice(self.viewport_sizes)
            driver.set_window_size(viewport[0], viewport[1])
            
            # Add small random offset
            offset_x = random.randint(-50, 50)
            offset_y = random.randint(-50, 50)
            driver.set_window_position(offset_x, offset_y)
            
            logger.info(f"Randomized viewport to: {viewport[0]}x{viewport[1]}")
        
        except Exception as e:
            logger.warning(f"Error randomizing viewport: {str(e)}")
    
    def simulate_human_behavior(self, driver):
        """
        Simulate enhanced human-like behavior patterns
        """
        try:
            # Get window size for realistic movements
            window_size = driver.get_window_size()
            max_x = window_size['width'] - 100
            max_y = window_size['height'] - 100
            
            # Random mouse movements with more realistic patterns
            actions = ActionChains(driver)
            
            # Simulate reading pattern - move eyes/mouse across page
            for _ in range(random.randint(3, 7)):
                # Move in reading-like patterns
                if random.random() < 0.6:  # Horizontal reading movement
                    start_x = random.randint(50, 200)
                    end_x = random.randint(300, max_x)
                    y = random.randint(100, max_y)
                    
                    # Move in steps to simulate reading
                    steps = random.randint(3, 6)
                    for step in range(steps):
                        x = start_x + (end_x - start_x) * step / steps
                        actions.move_to_element_with_offset(driver.find_element(By.TAG_NAME, 'body'), int(x), y)
                        actions.pause(random.uniform(0.2, 0.5))
                else:  # Vertical scrolling movement
                    x = random.randint(100, max_x)
                    y = random.randint(100, max_y)
                    actions.move_to_element_with_offset(driver.find_element(By.TAG_NAME, 'body'), x, y)
                    actions.pause(random.uniform(0.1, 0.3))
            
            actions.perform()
            
            # Simulate realistic scrolling behavior
            scroll_patterns = [
                'slow_read',  # Slow scrolling as if reading
                'quick_scan',  # Quick scrolling to scan content
                'back_and_forth'  # Scroll down then back up
            ]
            
            pattern = random.choice(scroll_patterns)
            
            if pattern == 'slow_read':
                for _ in range(random.randint(2, 4)):
                    scroll_amount = random.randint(100, 300)
                    driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                    time.sleep(random.uniform(1.5, 3.0))  # Reading time
            
            elif pattern == 'quick_scan':
                for _ in range(random.randint(3, 6)):
                    scroll_amount = random.randint(200, 500)
                    driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                    time.sleep(random.uniform(0.3, 0.8))  # Quick scanning
            
            elif pattern == 'back_and_forth':
                # Scroll down
                for _ in range(random.randint(2, 4)):
                    scroll_amount = random.randint(150, 400)
                    driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                    time.sleep(random.uniform(0.8, 1.5))
                
                # Scroll back up a bit
                scroll_back = random.randint(100, 200)
                driver.execute_script(f"window.scrollBy(0, -{scroll_back});")
                time.sleep(random.uniform(0.5, 1.0))
            
            # Simulate occasional hover over elements
            if random.random() < 0.4:
                try:
                    # Find some common elements to hover over
                    hoverable_elements = driver.find_elements(By.CSS_SELECTOR, 'a, button, [role="button"], input')
                    if hoverable_elements:
                        element = random.choice(hoverable_elements[:5])  # Only consider first 5 to avoid hidden elements
                        if element.is_displayed():
                            ActionChains(driver).move_to_element(element).pause(random.uniform(0.5, 1.5)).perform()
                except Exception:
                    pass  # Ignore hover errors
            
            # Random pause to simulate thinking/reading
            time.sleep(random.uniform(1.5, 4.0))
            
            logger.info("Simulated enhanced human behavior")
        
        except Exception as e:
            logger.warning(f"Error simulating human behavior: {str(e)}")
    
    def human_like_click(self, driver, element):
        """
        Perform human-like click with random delays and movements
        
        Args:
            driver: WebDriver instance
            element: Element to click
        """
        try:
            # Scroll element into view
            driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(random.uniform(0.5, 1.0))
            
            # Move to element with some randomness
            actions = ActionChains(driver)
            
            # Add some random movement before clicking
            offset_x = random.randint(-5, 5)
            offset_y = random.randint(-5, 5)
            
            actions.move_to_element_with_offset(element, offset_x, offset_y)
            actions.pause(random.uniform(0.1, 0.3))
            actions.click()
            actions.perform()
            
            # Random delay after click
            time.sleep(random.uniform(0.5, 1.5))
            
            logger.debug("Performed human-like click")
        
        except Exception as e:
            logger.error(f"Error performing human-like click: {str(e)}")
            # Fallback to regular click
            element.click()
    
    def human_like_type(self, element, text):
        """
        Type text with human-like delays and patterns
        
        Args:
            element: Input element
            text: Text to type
        """
        try:
            element.clear()
            time.sleep(random.uniform(0.2, 0.5))
            
            for char in text:
                element.send_keys(char)
                
                # Random typing speed
                if char == ' ':
                    # Longer pause for spaces
                    time.sleep(random.uniform(0.1, 0.3))
                else:
                    # Normal character delay
                    time.sleep(random.uniform(0.05, 0.15))
                
                # Occasional longer pauses (thinking)
                if random.random() < 0.1:
                    time.sleep(random.uniform(0.3, 0.8))
            
            # Final pause
            time.sleep(random.uniform(0.5, 1.0))
            
            logger.debug(f"Typed text with human-like pattern: {len(text)} characters")
        
        except Exception as e:
            logger.error(f"Error typing text: {str(e)}")
            # Fallback to regular send_keys
            element.clear()
            element.send_keys(text)
    
    def random_delay(self, min_seconds=1, max_seconds=5):
        """
        Add random delay to simulate human reading/thinking time
        
        Args:
            min_seconds: Minimum delay in seconds
            max_seconds: Maximum delay in seconds
        """
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
        logger.debug(f"Random delay: {delay:.2f} seconds")
    
    def handle_rate_limiting(self, driver, retry_count=0, max_retries=3):
        """
        Handle rate limiting and anti-bot measures
        
        Args:
            driver: WebDriver instance
            retry_count: Current retry attempt
            max_retries: Maximum number of retries
        
        Returns:
            bool: True if rate limiting was handled, False otherwise
        """
        try:
            # Check for common rate limiting indicators
            rate_limit_indicators = [
                'rate limit',
                'too many requests',
                'please wait',
                'try again later',
                'blocked',
                'captcha'
            ]
            
            page_text = driver.page_source.lower()
            
            for indicator in rate_limit_indicators:
                if indicator in page_text:
                    logger.warning(f"Rate limiting detected: {indicator}")
                    
                    if retry_count < max_retries:
                        # Exponential backoff
                        wait_time = (2 ** retry_count) * 60 + random.randint(30, 120)
                        logger.info(f"Waiting {wait_time} seconds before retry")
                        time.sleep(wait_time)
                        
                        # Change user agent and viewport
                        self._rotate_fingerprint(driver)
                        
                        return True
                    else:
                        logger.error(f"Max retries ({max_retries}) exceeded for rate limiting")
                        return False
            
            return True
        
        except Exception as e:
            logger.error(f"Error handling rate limiting: {str(e)}")
            return False
    
    def _rotate_fingerprint(self, driver):
        """
        Rotate browser fingerprint to avoid detection
        
        Args:
            driver: WebDriver instance
        """
        try:
            # Change user agent
            new_user_agent = self.ua.random
            driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                'userAgent': new_user_agent
            })
            
            # Change viewport
            self.randomize_viewport(driver)
            
            # Change geolocation
            self._set_random_geolocation(driver)
            
            # Clear some storage
            driver.execute_script("localStorage.clear();")
            driver.execute_script("sessionStorage.clear();")
            
            logger.info("Rotated browser fingerprint")
        
        except Exception as e:
            logger.warning(f"Error rotating fingerprint: {str(e)}")
    
    def detect_captcha(self, driver):
        """
        Detect if a captcha is present on the page
        
        Args:
            driver: WebDriver instance
        
        Returns:
            dict: Captcha detection result
        """
        try:
            captcha_selectors = [
                'iframe[src*="recaptcha"]',
                'iframe[src*="hcaptcha"]',
                '.captcha',
                '[data-testid="captcha"]',
                '.recaptcha',
                '.hcaptcha',
                '#captcha'
            ]
            
            for selector in captcha_selectors:
                try:
                    captcha_element = driver.find_element(By.CSS_SELECTOR, selector)
                    if captcha_element.is_displayed():
                        logger.warning(f"Captcha detected with selector: {selector}")
                        return {
                            'detected': True,
                            'type': self._identify_captcha_type(selector),
                            'element': captcha_element
                        }
                except:
                    continue
            
            # Check page text for captcha keywords
            page_text = driver.page_source.lower()
            captcha_keywords = ['captcha', 'verify you are human', 'robot check']
            
            for keyword in captcha_keywords:
                if keyword in page_text:
                    logger.warning(f"Captcha detected by keyword: {keyword}")
                    return {
                        'detected': True,
                        'type': 'unknown',
                        'element': None
                    }
            
            return {'detected': False, 'type': None, 'element': None}
        
        except Exception as e:
            logger.error(f"Error detecting captcha: {str(e)}")
            return {'detected': False, 'type': None, 'element': None}
    
    def _identify_captcha_type(self, selector):
        """
        Identify the type of captcha based on selector
        
        Args:
            selector: CSS selector that matched the captcha
        
        Returns:
            str: Captcha type
        """
        if 'recaptcha' in selector.lower():
            return 'recaptcha'
        elif 'hcaptcha' in selector.lower():
            return 'hcaptcha'
        else:
            return 'unknown'
    
    def wait_for_element_safely(self, driver, selector, timeout=10, method=By.CSS_SELECTOR):
        """
        Wait for element with anti-detection measures
        
        Args:
            driver: WebDriver instance
            selector: Element selector
            timeout: Timeout in seconds
            method: Selenium By method
        
        Returns:
            WebElement or None
        """
        try:
            # Add random delay before waiting
            time.sleep(random.uniform(0.5, 1.5))
            
            element = WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((method, selector))
            )
            
            # Additional wait for element to be stable
            time.sleep(random.uniform(0.2, 0.8))
            
            return element
        
        except TimeoutException:
            logger.warning(f"Element not found within {timeout} seconds: {selector}")
            return None
        except Exception as e:
            logger.error(f"Error waiting for element: {str(e)}")
            return None