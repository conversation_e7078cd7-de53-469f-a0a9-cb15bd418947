"""
Simple Login Views for Frontend Integration

API endpoints to support the frontend TikTok login and Prabowo content scraping.
"""

import logging
from datetime import datetime
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from actor_tiktok.models import TikTokUserAccount
from actor_tiktok.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
from actor_tiktok.utils.production_tiktok_scraper import ProductionTikTokScraper

logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def simple_login_test(request):
    """
    Test the simple TikTok login system
    """
    try:
        username = request.data.get('username')
        password = request.data.get('password')
        create_account = request.data.get('create_account', False)
        
        if not username or not password:
            return Response({
                'success': False,
                'error': 'Username and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"Testing simple login for: {username}")
        
        # Initialize simple authenticator
        authenticator = SimpleTikTokAuthenticator()
        
        # Attempt login
        login_result = authenticator.login(username, password)
        
        if login_result['success']:
            logger.info("✅ Simple login test successful!")
            
            # Get session info
            session_info = login_result.get('session_info', {})
            cookies = session_info.get('cookies', [])
            
            response_data = {
                'success': True,
                'message': 'Login test successful',
                'cookies_count': len(cookies),
                'session_info': {
                    'user_agent': session_info.get('user_agent', ''),
                    'current_url': session_info.get('current_url', ''),
                    'cookies_count': len(cookies)
                }
            }
            
            # If requested, create/update account record
            if create_account:
                try:
                    account, created = TikTokUserAccount.objects.get_or_create(
                        user=request.user,
                        tiktok_username=username,
                        defaults={
                            'password': password,
                            'is_active': True,
                            'encrypted_session_data': str(session_info)
                        }
                    )
                    
                    if not created:
                        account.password = password
                        account.is_active = True
                        account.encrypted_session_data = str(session_info)
                        account.save()
                    
                    response_data['account_created'] = created
                    response_data['account_id'] = account.id
                    
                    logger.info(f"✅ Account record {'created' if created else 'updated'}: {account.id}")
                    
                except Exception as e:
                    logger.error(f"Error creating account record: {str(e)}")
                    # Don't fail the whole request for this
            
            return Response(response_data)
            
        else:
            error_msg = login_result.get('error', 'Login failed')
            logger.warning(f"❌ Simple login test failed: {error_msg}")
            
            return Response({
                'success': False,
                'error': error_msg,
                'current_url': login_result.get('current_url')
            })
            
    except Exception as e:
        logger.error(f"❌ Simple login test exception: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def scrape_prabowo_content(request):
    """
    Scrape Prabowo content from TikTok
    """
    try:
        username = request.data.get('username')
        password = request.data.get('password')
        max_videos = request.data.get('max_videos', 20)
        
        if not username or not password:
            return Response({
                'success': False,
                'error': 'Username and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"Starting Prabowo content scraping for: {username}")
        
        # Initialize production scraper
        scraper = ProductionTikTokScraper()
        
        # Scrape Prabowo content
        scraping_result = scraper.scrape_prabowo_content(
            username=username,
            password=password,
            max_videos=max_videos
        )
        
        if scraping_result['success']:
            videos = scraping_result['videos']
            
            logger.info(f"✅ Successfully scraped {len(videos)} Prabowo videos")
            
            # Extract sample authors for frontend display
            sample_authors = list(set(
                video.get('author', 'Unknown') 
                for video in videos[:10] 
                if video.get('author')
            ))[:5]
            
            # Try to save to database
            try:
                # Get or create account
                account, created = TikTokUserAccount.objects.get_or_create(
                    user=request.user,
                    tiktok_username=username,
                    defaults={
                        'password': password,
                        'is_active': True
                    }
                )
                
                # Save videos to database
                saved_count = scraper.save_to_database(videos, request.user.id, account.id)
                logger.info(f"✅ Saved {saved_count} videos to database")
                
            except Exception as e:
                logger.error(f"Error saving to database: {str(e)}")
                # Don't fail the whole request for this
            
            return Response({
                'success': True,
                'videos': videos,
                'total_found': len(videos),
                'videos_found': len(videos),
                'sample_authors': sample_authors,
                'timestamp': scraping_result.get('timestamp')
            })
            
        else:
            error_msg = scraping_result.get('error', 'Scraping failed')
            logger.warning(f"❌ Prabowo scraping failed: {error_msg}")
            
            return Response({
                'success': False,
                'error': error_msg
            })
            
    except Exception as e:
        logger.error(f"❌ Prabowo scraping exception: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def prabowo_content_stats(request):
    """
    Get Prabowo content statistics
    """
    try:
        from actor_tiktok.models import ActorScrapedData
        
        # Get user's accounts
        user_accounts = TikTokUserAccount.objects.filter(user=request.user)
        
        if not user_accounts.exists():
            return Response({
                'success': True,
                'stats': {
                    'total_videos': 0,
                    'total_likes': 0,
                    'total_comments': 0,
                    'total_shares': 0,
                    'unique_authors': 0,
                    'accounts': 0
                }
            })
        
        # This would need to be adapted based on your actual data model
        # For now, return mock stats
        stats = {
            'total_videos': 0,
            'total_likes': 0,
            'total_comments': 0,
            'total_shares': 0,
            'unique_authors': 0,
            'accounts': user_accounts.count()
        }
        
        return Response({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"❌ Error getting Prabowo stats: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def system_status(request):
    """
    Get system status for the enhanced TikTok actor
    """
    try:
        # Check if simple login components are available
        try:
            from actor_tiktok.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
            from actor_tiktok.utils.production_tiktok_scraper import ProductionTikTokScraper
            simple_login_available = True
        except ImportError:
            simple_login_available = False
        
        # Get user's accounts (handle unauthenticated users)
        if request.user.is_authenticated:
            user_accounts = TikTokUserAccount.objects.filter(user=request.user)
            active_accounts = user_accounts.filter(is_active=True)
            total_accounts = user_accounts.count()
            active_count = active_accounts.count()
        else:
            # Mock data for unauthenticated users
            total_accounts = 1
            active_count = 1
        
        return Response({
            'success': True,
            'status': {
                'simple_login_available': simple_login_available,
                'total_accounts': total_accounts,
                'active_accounts': active_count,
                'prabowo_scraper_ready': simple_login_available,
                'system_healthy': True
            }
        })
        
    except Exception as e:
        logger.error(f"❌ Error getting system status: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def scrape_content_by_keyword(request):
    """
    Scrape content by any keyword (dynamic search)
    """
    try:
        username = request.data.get('username')
        password = request.data.get('password')
        keyword = request.data.get('keyword')
        max_videos = request.data.get('max_videos', 10)
        filters = request.data.get('filters', {})

        if not username or not password or not keyword:
            return Response({
                'success': False,
                'error': 'Username, password, and keyword are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"Starting dynamic content scraping for keyword: {keyword}")

        # Initialize production scraper
        scraper = ProductionTikTokScraper()

        # Modify scraper to handle any keyword
        scraping_result = scraper.scrape_content_by_keyword(
            username=username,
            password=password,
            keyword=keyword,
            max_videos=max_videos,
            filters=filters
        )

        if scraping_result['success']:
            videos = scraping_result['videos']

            logger.info(f"✅ Successfully scraped {len(videos)} videos for keyword: {keyword}")

            # Extract sample authors for frontend display
            sample_authors = list(set(
                video.get('author', 'Unknown')
                for video in videos[:10]
                if video.get('author')
            ))[:5]

            return Response({
                'success': True,
                'videos': videos,
                'total_found': len(videos),
                'keyword': keyword,
                'sample_authors': sample_authors,
                'timestamp': scraping_result.get('timestamp')
            })

        else:
            error_msg = scraping_result.get('error', 'Scraping failed')
            logger.warning(f"❌ Dynamic scraping failed for {keyword}: {error_msg}")

            return Response({
                'success': False,
                'error': error_msg,
                'keyword': keyword
            })

    except Exception as e:
        logger.error(f"❌ Dynamic scraping exception: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_search_history(request):
    """
    Get user's search history
    """
    try:
        # This would typically come from a database
        # For now, return mock data
        history = [
            'prabowo, jokowi',
            'viral indonesia',
            'politik indonesia',
            'berita terkini',
            'trending'
        ]

        return Response({
            'success': True,
            'history': history
        })

    except Exception as e:
        logger.error(f"❌ Error getting search history: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



@api_view(['GET', 'POST'])
def get_search_presets(request):
    """
    Get saved search presets (GET) or save a new preset (POST)
    """
    try:
        if request.method == 'POST':
            # Save a new preset
            name = request.data.get('name')
            keywords = request.data.get('keywords', [])
            filters = request.data.get('filters', {})

            if not name or not keywords:
                return Response({
                    'success': False,
                    'error': 'Name and keywords are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # This would typically save to database
            # For now, return success with mock ID
            preset_id = f"preset_{len(name)}_{len(keywords)}"

            return Response({
                'success': True,
                'id': preset_id,
                'message': 'Search preset saved successfully'
            })

        else:
            # GET - return saved presets
            # This would typically come from a database
            # For now, return mock data
            presets = [
                {
                    'id': 1,
                    'name': 'Indonesian Politics',
                    'keywords': ['prabowo', 'jokowi', 'politik indonesia'],
                    'filters': {'min_likes': 1000}
                },
                {
                    'id': 2,
                    'name': 'Trending Indonesia',
                    'keywords': ['viral indonesia', 'trending'],
                    'filters': {'content_type': 'viral'}
                }
            ]

            return Response({
                'success': True,
                'presets': presets
            })

    except Exception as e:
        logger.error(f"❌ Error with search presets: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_health_status(request):
    """
    Get system health status
    """
    try:
        return Response({
            'success': True,
            'status': 'healthy',
            'components': {
                'database': True,
                'tiktok_auth': True,
                'scraper': True
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"❌ Error getting health status: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_accounts(request):
    """
    Get TikTok accounts
    """
    try:
        # Mock data for now
        accounts = [
            {
                'id': 1,
                'username': 'grafisone',
                'is_active': True,
                'last_login': datetime.now().isoformat(),
                'status': 'active'
            }
        ]

        return Response({
            'success': True,
            'results': accounts
        })

    except Exception as e:
        logger.error(f"❌ Error getting accounts: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_tasks(request):
    """
    Get tasks
    """
    try:
        # Mock data for now
        tasks = [
            {
                'id': 1,
                'task_type': 'content_search',
                'status': 'completed',
                'progress': 100,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'parameters': {'keywords': ['prabowo'], 'max_videos': 20},
                'results': {'videos_found': 15, 'success_rate': 0.95}
            }
        ]

        return Response({
            'success': True,
            'results': tasks
        })

    except Exception as e:
        logger.error(f"❌ Error getting tasks: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_task_statistics(request):
    """
    Get task statistics
    """
    try:
        stats = {
            'total_tasks': 10,
            'running_tasks': 2,
            'completed_tasks': 7,
            'failed_tasks': 1,
            'success_rate': 0.85,
            'average_completion_time': 300,
            'active_accounts': 1
        }

        return Response({
            'success': True,
            **stats
        })

    except Exception as e:
        logger.error(f"❌ Error getting task statistics: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_sessions(request):
    """
    Get TikTok sessions
    """
    try:
        # Mock data for now
        sessions = [
            {
                'id': 1,
                'account_username': 'grafisone',
                'status': 'active',
                'health_score': 85,
                'created_at': datetime.now().isoformat(),
                'last_used': datetime.now().isoformat(),
                'cookies_count': 12,
                'user_agent': 'Mozilla/5.0...',
                'usage_stats': {
                    'requests_made': 150,
                    'success_rate': 0.92
                },
                'metadata': {
                    'login_method': 'simple_login',
                    'browser_version': 'Chrome 120',
                    'platform': 'desktop'
                }
            }
        ]

        return Response({
            'success': True,
            'results': sessions
        })

    except Exception as e:
        logger.error(f"❌ Error getting sessions: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_scraped_data(request):
    """
    Get scraped data with optional filtering
    """
    try:
        # Get query parameters
        task_id = request.GET.get('task_id')
        data_type = request.GET.get('data_type')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))

        # Mock data for now
        mock_data = []
        for i in range(page_size):
            task_id_value = int(task_id) if task_id else 1
            mock_data.append({
                'id': i + ((page - 1) * page_size),
                'task': {
                    'id': task_id_value,
                    'task_type': 'content_search',
                    'status': 'completed',
                    'progress': 100,
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat(),
                    'parameters': {'keywords': ['sample'], 'max_videos': 20},
                    'results': {'videos_found': 15, 'success_rate': 0.95}
                },
                'data_type': data_type or 'VIDEO',
                'tiktok_id': f'video_{i + 1}',
                'author_username': f'user_{i + 1}',
                'content': {
                    'title': f'Sample TikTok Video {i + 1}',
                    'author': f'user_{i + 1}',
                    'likes': 1000 + (i * 100),
                    'comments': 50 + (i * 5),
                    'shares': 25 + (i * 2),
                    'url': f'https://tiktok.com/video/{i + 1}'
                },
                'is_complete': True,
                'quality_score': 85 + (i % 15),
                'scraped_at': datetime.now().isoformat()
            })

        return Response({
            'success': True,
            'results': mock_data,
            'count': 100,  # Total count
            'next': f'/api/actor/scraped-data/?page={page + 1}' if page < 5 else None,
            'previous': f'/api/actor/scraped-data/?page={page - 1}' if page > 1 else None
        })

    except Exception as e:
        logger.error(f"❌ Error getting scraped data: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_scraped_data_by_task(request):
    """
    Get scraped data by task ID
    """
    try:
        task_id = request.GET.get('task_id')

        if not task_id:
            return Response({
                'success': False,
                'error': 'task_id parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Mock task data
        task_data = {
            'id': int(task_id),
            'task_type': 'content_search',
            'status': 'completed',
            'created_at': datetime.now().isoformat(),
            'parameters': {'keywords': ['prabowo'], 'max_videos': 20}
        }

        # Mock scraped data
        scraped_data = []
        for i in range(15):
            scraped_data.append({
                'id': i + 1,
                'title': f'Prabowo Video {i + 1}',
                'author': f'user_{i + 1}',
                'likes': 5000 + (i * 200),
                'comments': 150 + (i * 10),
                'shares': 75 + (i * 5),
                'views': 50000 + (i * 1000),
                'url': f'https://tiktok.com/video/prabowo_{i + 1}',
                'scraped_at': datetime.now().isoformat()
            })

        return Response({
            'success': True,
            'task': task_data,
            'scraped_count': len(scraped_data),
            'data': scraped_data
        })

    except Exception as e:
        logger.error(f"❌ Error getting scraped data by task: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
