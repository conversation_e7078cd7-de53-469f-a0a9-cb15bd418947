import logging
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from django.db.models import Count, Avg, Q
from django.contrib.auth.models import User
from ..models import TikTokUserAccount, ActorTask, ActorScrapedData, TikTokSession
from ..serializers import (
    TikTokUserAccountSerializer, TikTokLoginSerializer, ActorTaskSerializer,
    ActorTaskCreateSerializer, ActorScrapedDataSerializer, TikTokSessionSerializer,
    TaskStatsSerializer
)
from ..tasks import (
    actor_login_task, actor_scrape_my_videos_task, actor_scrape_my_followers_task,
    actor_scrape_my_following_task, actor_scrape_my_likes_task,
    actor_scrape_feed_task, actor_scrape_targeted_user_task, actor_scrape_hashtag_task,
    actor_scrape_competitor_task
)
from ..filters import <PERSON><PERSON><PERSON><PERSON>ilter, ActorScrapedDataFilter
from ..utils.tiktok_auth import TikTokAuthenticator
from ..utils.anti_detection import AntiDetectionManager

logger = logging.getLogger(__name__)

class TikTokUserAccountViewSet(viewsets.ModelViewSet):
    """ViewSet for managing TikTok user accounts"""
    serializer_class = TikTokUserAccountSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_active', 'is_blocked']
    
    def get_queryset(self):
        """Return only the current user's TikTok accounts"""
        return TikTokUserAccount.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        """Associate the account with the current user"""
        serializer.save(user=self.request.user)
    
    @action(detail=True, methods=['post'])
    def refresh_session(self, request, pk=None):
        """Refresh TikTok session for an account"""
        account = self.get_object()
        
        if not account.is_session_valid():
            return Response(
                {'error': 'Account session is invalid. Please login again.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Trigger session refresh task
            from .tasks import refresh_tiktok_session_task
            task = refresh_tiktok_session_task.delay(account.id)
            
            return Response({
                'message': 'Session refresh initiated',
                'task_id': task.id
            }, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Failed to refresh session for account {account.id}: {str(e)}")
            return Response(
                {'error': f'Failed to refresh session: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def logout(self, request, pk=None):
        """Logout from TikTok account"""
        account = self.get_object()
        
        try:
            # Clear session data
            account.encrypted_session_data = '{}'
            account.is_active = False
            account.session_expires_at = timezone.now()
            account.save()
            
            # Cancel any running tasks for this account
            ActorTask.objects.filter(
                tiktok_account=account,
                status__in=['PENDING', 'RUNNING']
            ).update(status='CANCELLED')
            
            return Response(
                {'message': 'Successfully logged out from TikTok account'},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Failed to logout from account {account.id}: {str(e)}")
            return Response(
                {'error': f'Failed to logout: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class TikTokLoginView(APIView):
    """View for TikTok login authentication"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """Login to TikTok with user credentials"""
        serializer = TikTokLoginSerializer(data=request.data)
        
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        tiktok_username = serializer.validated_data['tiktok_username']
        tiktok_password = serializer.validated_data['tiktok_password']
        use_2fa = serializer.validated_data.get('use_2fa', False)
        two_factor_code = serializer.validated_data.get('two_factor_code')
        remember_session = serializer.validated_data.get('remember_session', True)
        
        # Check if account already exists and is blocked
        try:
            existing_account = TikTokUserAccount.objects.get(
                user=request.user,
                tiktok_username=tiktok_username
            )
            
            if existing_account.is_blocked:
                if existing_account.blocked_until and existing_account.blocked_until > timezone.now():
                    return Response({
                        'error': f'Account is temporarily blocked until {existing_account.blocked_until}'
                    }, status=status.HTTP_429_TOO_MANY_REQUESTS)
                else:
                    # Unblock if block period has expired
                    existing_account.is_blocked = False
                    existing_account.blocked_until = None
                    existing_account.save()
        
        except TikTokUserAccount.DoesNotExist:
            existing_account = None
        
        try:
            # Initiate TikTok login task
            task = actor_login_task.delay(
                user_id=request.user.id,
                tiktok_username=tiktok_username,
                tiktok_password=tiktok_password,
                use_2fa=use_2fa,
                two_factor_code=two_factor_code,
                remember_session=remember_session
            )
            
            return Response({
                'message': 'TikTok login initiated',
                'task_id': task.id,
                'status': 'pending'
            }, status=status.HTTP_202_ACCEPTED)
        
        except Exception as e:
            logger.error(f"Failed to initiate TikTok login: {str(e)}")
            
            # Increment login attempts if account exists
            if existing_account:
                existing_account.increment_login_attempts()
            
            return Response(
                {'error': f'Failed to initiate login: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class ActorTaskViewSet(viewsets.ModelViewSet):
    """ViewSet for managing actor tasks"""
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = ActorTaskFilter
    
    def get_queryset(self):
        """Return only the current user's tasks"""
        return ActorTask.objects.filter(user=self.request.user).select_related(
            'tiktok_account', 'user'
        ).prefetch_related('scraped_data')
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'create':
            return ActorTaskCreateSerializer
        return ActorTaskSerializer
    
    def perform_create(self, serializer):
        """Create a new actor task and start it"""
        task = serializer.save()
        
        # Dispatch appropriate Celery task based on task type
        try:
            if task.task_type == 'MY_VIDEOS':
                celery_task = actor_scrape_my_videos_task.delay(task.id)
            elif task.task_type == 'MY_FOLLOWERS':
                celery_task = actor_scrape_my_followers_task.delay(task.id)
            elif task.task_type == 'MY_FOLLOWING':
                celery_task = actor_scrape_my_following_task.delay(task.id)
            elif task.task_type == 'MY_LIKES':
                celery_task = actor_scrape_my_likes_task.delay(task.id)
            elif task.task_type == 'FEED_SCRAPE':
                celery_task = actor_scrape_feed_task.delay(task.id)
            elif task.task_type == 'TARGETED_USER':
                celery_task = actor_scrape_targeted_user_task.delay(task.id)
            elif task.task_type == 'HASHTAG_ANALYSIS':
                celery_task = actor_scrape_hashtag_task.delay(task.id)
            elif task.task_type == 'COMPETITOR_ANALYSIS':
                celery_task = actor_scrape_competitor_task.delay(task.id)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")
            
            task.celery_task_id = celery_task.id
            task.save()
            
        except Exception as e:
            logger.error(f"Failed to start task {task.id}: {str(e)}")
            task.status = 'FAILED'
            task.error_message = f"Failed to start task: {str(e)}"
            task.save()
    
    @action(detail=True, methods=['post'])
    def pause(self, request, pk=None):
        """Pause a running task"""
        task = self.get_object()
        
        if task.status != 'RUNNING':
            return Response(
                {'error': 'Can only pause running tasks'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Update task status
            task.status = 'PAUSED'
            task.save()
            
            # Revoke Celery task if it exists
            if task.celery_task_id:
                from celery import current_app
                current_app.control.revoke(task.celery_task_id, terminate=True)
            
            return Response(
                {'message': 'Task paused successfully'},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Failed to pause task {task.id}: {str(e)}")
            return Response(
                {'error': f'Failed to pause task: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def resume(self, request, pk=None):
        """Resume a paused task"""
        task = self.get_object()
        
        if task.status != 'PAUSED':
            return Response(
                {'error': 'Can only resume paused tasks'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Check if TikTok account is still valid
            if not task.tiktok_account.is_session_valid():
                return Response(
                    {'error': 'TikTok account session is invalid. Please login again.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Restart the task
            task.status = 'PENDING'
            task.save()
            
            # Re-dispatch Celery task
            self.perform_create(type('obj', (), {'save': lambda: task})())
            
            return Response(
                {'message': 'Task resumed successfully'},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Failed to resume task {task.id}: {str(e)}")
            return Response(
                {'error': f'Failed to resume task: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a task"""
        task = self.get_object()
        
        if task.status in ['COMPLETED', 'FAILED', 'CANCELLED']:
            return Response(
                {'error': 'Cannot cancel a completed, failed, or already cancelled task'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Update task status
            task.status = 'CANCELLED'
            task.completed_at = timezone.now()
            task.save()
            
            # Revoke Celery task if it exists
            if task.celery_task_id:
                from celery import current_app
                current_app.control.revoke(task.celery_task_id, terminate=True)
            
            return Response(
                {'message': 'Task cancelled successfully'},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Failed to cancel task {task.id}: {str(e)}")
            return Response(
                {'error': f'Failed to cancel task: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def retry(self, request, pk=None):
        """Retry a failed or cancelled task"""
        task = self.get_object()
        
        if task.status not in ['FAILED', 'CANCELLED']:
            return Response(
                {'error': 'Can only retry failed or cancelled tasks'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Check if TikTok account is still valid
            if not task.tiktok_account.is_session_valid():
                return Response(
                    {'error': 'TikTok account session is invalid. Please login again.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Reset task status and clear previous error
            task.status = 'PENDING'
            task.error_message = ''
            task.started_at = None
            task.completed_at = None
            task.celery_task_id = None
            task.save()
            
            # Dispatch appropriate Celery task based on task type
            if task.task_type == 'MY_VIDEOS':
                celery_task = actor_scrape_my_videos_task.delay(task.id)
            elif task.task_type == 'MY_FOLLOWERS':
                celery_task = actor_scrape_my_followers_task.delay(task.id)
            elif task.task_type == 'MY_FOLLOWING':
                celery_task = actor_scrape_my_following_task.delay(task.id)
            elif task.task_type == 'MY_LIKES':
                celery_task = actor_scrape_my_likes_task.delay(task.id)
            elif task.task_type == 'FEED_SCRAPE':
                celery_task = actor_scrape_feed_task.delay(task.id)
            elif task.task_type == 'TARGETED_USER':
                celery_task = actor_scrape_targeted_user_task.delay(task.id)
            elif task.task_type == 'HASHTAG_ANALYSIS':
                celery_task = actor_scrape_hashtag_task.delay(task.id)
            elif task.task_type == 'COMPETITOR_ANALYSIS':
                celery_task = actor_scrape_competitor_task.delay(task.id)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")
            
            task.celery_task_id = celery_task.id
            task.save()
            
            return Response(
                {'message': 'Task retry initiated successfully'},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Failed to retry task {task.id}: {str(e)}")
            task.status = 'FAILED'
            task.error_message = f"Failed to retry task: {str(e)}"
            task.save()
            return Response(
                {'error': f'Failed to retry task: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get task statistics for the current user"""
        try:
            user_tasks = self.get_queryset()
            
            stats = {
                'total_tasks': user_tasks.count(),
                'pending_tasks': user_tasks.filter(status='PENDING').count(),
                'running_tasks': user_tasks.filter(status='RUNNING').count(),
                'completed_tasks': user_tasks.filter(status='COMPLETED').count(),
                'failed_tasks': user_tasks.filter(status='FAILED').count(),
                'total_items_scraped': sum(task.items_scraped for task in user_tasks),
                'active_accounts': TikTokUserAccount.objects.filter(
                    user=request.user, is_active=True
                ).count()
            }
            
            # Calculate success rate
            total_completed = stats['completed_tasks'] + stats['failed_tasks']
            if total_completed > 0:
                stats['success_rate'] = (stats['completed_tasks'] / total_completed) * 100
            else:
                stats['success_rate'] = 0.0
            
            # Calculate average completion time
            completed_tasks = user_tasks.filter(
                status='COMPLETED',
                started_at__isnull=False,
                completed_at__isnull=False
            )
            
            if completed_tasks.exists():
                total_duration = sum(
                    (task.completed_at - task.started_at).total_seconds()
                    for task in completed_tasks
                )
                stats['average_completion_time'] = total_duration / completed_tasks.count()
            else:
                stats['average_completion_time'] = 0.0
            
            serializer = TaskStatsSerializer(stats)
            return Response(serializer.data, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error(f"Failed to get task statistics: {str(e)}")
            return Response(
                {'error': f'Failed to get statistics: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class ActorScrapedDataViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing scraped data from actor tasks"""
    serializer_class = ActorScrapedDataSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = ActorScrapedDataFilter
    
    def get_queryset(self):
        """Return only scraped data from the current user's tasks"""
        return ActorScrapedData.objects.filter(
            task__user=self.request.user
        ).select_related('task').order_by('-scraped_at')

class TikTokSessionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing TikTok session information"""
    serializer_class = TikTokSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_healthy']
    
    def get_queryset(self):
        """Return only sessions from the current user's TikTok accounts"""
        return TikTokSession.objects.filter(
            tiktok_account__user=self.request.user
        ).select_related('tiktok_account').order_by('-created_at')

class MultiAccountLoginView(APIView):
    """View for multi-account TikTok login with automatic switching"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """Login to multiple TikTok accounts with automatic switching on bot detection"""
        try:
            account_credentials = request.data.get('accounts', [])
            headless = request.data.get('headless', True)
            
            if not account_credentials:
                return Response(
                    {'error': 'No account credentials provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Validate account credentials format
            for i, account in enumerate(account_credentials):
                if not isinstance(account, dict):
                    return Response(
                        {'error': f'Account {i+1} must be an object with username/email and password'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                username = account.get('username') or account.get('email')
                password = account.get('password')
                
                if not username or not password:
                    return Response(
                        {'error': f'Account {i+1} missing username/email or password'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Start multi-account login task
            from .tasks import multi_account_login_task
            
            task_result = multi_account_login_task.delay(
                user_id=request.user.id,
                account_credentials_list=account_credentials,
                headless=headless
            )
            
            return Response({
                'message': 'Multi-account login task started',
                'task_id': task_result.id,
                'total_accounts': len(account_credentials),
                'status': 'PENDING'
            }, status=status.HTTP_202_ACCEPTED)
            
        except Exception as e:
            logger.error(f"Error starting multi-account login: {str(e)}")
            return Response(
                {'error': f'Failed to start multi-account login: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def get(self, request):
        """Get status of multi-account login task"""
        task_id = request.query_params.get('task_id')
        
        if not task_id:
            return Response(
                {'error': 'task_id parameter required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from celery.result import AsyncResult
            
            task_result = AsyncResult(task_id)
            
            if task_result.state == 'PENDING':
                response = {
                    'status': 'PENDING',
                    'message': 'Task is waiting to be processed'
                }
            elif task_result.state == 'PROGRESS':
                response = {
                    'status': 'PROGRESS',
                    'message': 'Task is currently running',
                    'current': task_result.info.get('current', 0),
                    'total': task_result.info.get('total', 1)
                }
            elif task_result.state == 'SUCCESS':
                result = task_result.result
                response = {
                    'status': 'SUCCESS',
                    'message': 'Multi-account login completed',
                    'result': result
                }
            else:
                # Task failed
                response = {
                    'status': 'FAILURE',
                    'message': 'Multi-account login failed',
                    'error': str(task_result.info)
                }
            
            return Response(response, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error checking task status: {str(e)}")
            return Response(
                {'error': f'Failed to check task status: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class HealthCheckView(APIView):
    """Health check endpoint for the actor_tiktok app"""
    permission_classes = []
    
    def get(self, request):
        """Return health status with metrics"""
        try:
            # Check database connectivity
            total_accounts = TikTokUserAccount.objects.count()
            
            # Check Celery connectivity
            from celery import current_app
            inspect = current_app.control.inspect()
            active_workers = inspect.active()
            celery_workers_count = len(active_workers) if active_workers else 0
            
            # Calculate metrics
            from django.db.models import Count, Q
            from datetime import timedelta
            
            # Get recent tasks (last 24 hours)
            recent_time = timezone.now() - timedelta(hours=24)
            recent_tasks = ActorTask.objects.filter(created_at__gte=recent_time)
            
            total_recent_tasks = recent_tasks.count()
            completed_tasks = recent_tasks.filter(status='COMPLETED').count()
            failed_tasks = recent_tasks.filter(status='FAILED').count()
            
            # Calculate success rate
            total_finished = completed_tasks + failed_tasks
            success_rate = (completed_tasks / total_finished * 100) if total_finished > 0 else 100.0
            error_rate = (failed_tasks / total_finished * 100) if total_finished > 0 else 0.0
            
            # Calculate average response time (using completion time as proxy)
            completed_with_times = recent_tasks.filter(
                status='COMPLETED',
                started_at__isnull=False,
                completed_at__isnull=False
            )
            
            if completed_with_times.exists():
                total_duration = sum(
                    (task.completed_at - task.started_at).total_seconds()
                    for task in completed_with_times
                )
                average_response_time = total_duration / completed_with_times.count()
            else:
                average_response_time = 0.0
            
            # Count active sessions and healthy accounts
            active_sessions = TikTokSession.objects.filter(is_healthy=True).count()
            healthy_accounts = TikTokUserAccount.objects.filter(is_active=True).count()
            
            # Determine overall status
            if success_rate < 50 or celery_workers_count == 0:
                overall_status = 'critical'
                message = 'System experiencing critical issues'
            elif success_rate < 80 or error_rate > 20:
                overall_status = 'warning'
                message = 'System performance degraded'
            else:
                overall_status = 'healthy'
                message = 'All systems operational'
            
            return Response({
                'status': overall_status,
                'message': message,
                'timestamp': timezone.now(),
                'database': 'connected',
                'celery_workers': celery_workers_count,
                'metrics': {
                    'success_rate': round(success_rate, 2),
                    'error_rate': round(error_rate, 2),
                    'average_response_time': round(average_response_time, 2),
                    'active_sessions': active_sessions,
                    'healthy_accounts': healthy_accounts
                }
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response({
                'status': 'critical',
                'message': f'Health check failed: {str(e)}',
                'timestamp': timezone.now(),
                'metrics': {
                    'success_rate': 0,
                    'error_rate': 100.0,
                    'average_response_time': 0,
                    'active_sessions': 0,
                    'healthy_accounts': 0
                }
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
