"""
Simple Login Views for Frontend Integration

API endpoints to support the frontend TikTok login and Prabowo content scraping.
"""

import logging
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from actor_tiktok.models import TikTokUserAccount
from actor_tiktok.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
from actor_tiktok.utils.production_tiktok_scraper import ProductionTikTokScraper

logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def simple_login_test(request):
    """
    Test the simple TikTok login system
    """
    try:
        username = request.data.get('username')
        password = request.data.get('password')
        create_account = request.data.get('create_account', False)
        
        if not username or not password:
            return Response({
                'success': False,
                'error': 'Username and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"Testing simple login for: {username}")
        
        # Initialize simple authenticator
        authenticator = SimpleTikTokAuthenticator()
        
        # Attempt login
        login_result = authenticator.login(username, password)
        
        if login_result['success']:
            logger.info("✅ Simple login test successful!")
            
            # Get session info
            session_info = login_result.get('session_info', {})
            cookies = session_info.get('cookies', [])
            
            response_data = {
                'success': True,
                'message': 'Login test successful',
                'cookies_count': len(cookies),
                'session_info': {
                    'user_agent': session_info.get('user_agent', ''),
                    'current_url': session_info.get('current_url', ''),
                    'cookies_count': len(cookies)
                }
            }
            
            # If requested, create/update account record
            if create_account:
                try:
                    account, created = TikTokUserAccount.objects.get_or_create(
                        user=request.user,
                        tiktok_username=username,
                        defaults={
                            'password': password,
                            'is_active': True,
                            'encrypted_session_data': str(session_info)
                        }
                    )
                    
                    if not created:
                        account.password = password
                        account.is_active = True
                        account.encrypted_session_data = str(session_info)
                        account.save()
                    
                    response_data['account_created'] = created
                    response_data['account_id'] = account.id
                    
                    logger.info(f"✅ Account record {'created' if created else 'updated'}: {account.id}")
                    
                except Exception as e:
                    logger.error(f"Error creating account record: {str(e)}")
                    # Don't fail the whole request for this
            
            return Response(response_data)
            
        else:
            error_msg = login_result.get('error', 'Login failed')
            logger.warning(f"❌ Simple login test failed: {error_msg}")
            
            return Response({
                'success': False,
                'error': error_msg,
                'current_url': login_result.get('current_url')
            })
            
    except Exception as e:
        logger.error(f"❌ Simple login test exception: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def scrape_prabowo_content(request):
    """
    Scrape Prabowo content from TikTok
    """
    try:
        username = request.data.get('username')
        password = request.data.get('password')
        max_videos = request.data.get('max_videos', 20)
        
        if not username or not password:
            return Response({
                'success': False,
                'error': 'Username and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"Starting Prabowo content scraping for: {username}")
        
        # Initialize production scraper
        scraper = ProductionTikTokScraper()
        
        # Scrape Prabowo content
        scraping_result = scraper.scrape_prabowo_content(
            username=username,
            password=password,
            max_videos=max_videos
        )
        
        if scraping_result['success']:
            videos = scraping_result['videos']
            
            logger.info(f"✅ Successfully scraped {len(videos)} Prabowo videos")
            
            # Extract sample authors for frontend display
            sample_authors = list(set(
                video.get('author', 'Unknown') 
                for video in videos[:10] 
                if video.get('author')
            ))[:5]
            
            # Try to save to database
            try:
                # Get or create account
                account, created = TikTokUserAccount.objects.get_or_create(
                    user=request.user,
                    tiktok_username=username,
                    defaults={
                        'password': password,
                        'is_active': True
                    }
                )
                
                # Save videos to database
                saved_count = scraper.save_to_database(videos, request.user.id, account.id)
                logger.info(f"✅ Saved {saved_count} videos to database")
                
            except Exception as e:
                logger.error(f"Error saving to database: {str(e)}")
                # Don't fail the whole request for this
            
            return Response({
                'success': True,
                'videos': videos,
                'total_found': len(videos),
                'videos_found': len(videos),
                'sample_authors': sample_authors,
                'timestamp': scraping_result.get('timestamp')
            })
            
        else:
            error_msg = scraping_result.get('error', 'Scraping failed')
            logger.warning(f"❌ Prabowo scraping failed: {error_msg}")
            
            return Response({
                'success': False,
                'error': error_msg
            })
            
    except Exception as e:
        logger.error(f"❌ Prabowo scraping exception: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def prabowo_content_stats(request):
    """
    Get Prabowo content statistics
    """
    try:
        from actor_tiktok.models import ActorScrapedData
        
        # Get user's accounts
        user_accounts = TikTokUserAccount.objects.filter(user=request.user)
        
        if not user_accounts.exists():
            return Response({
                'success': True,
                'stats': {
                    'total_videos': 0,
                    'total_likes': 0,
                    'total_comments': 0,
                    'total_shares': 0,
                    'unique_authors': 0,
                    'accounts': 0
                }
            })
        
        # This would need to be adapted based on your actual data model
        # For now, return mock stats
        stats = {
            'total_videos': 0,
            'total_likes': 0,
            'total_comments': 0,
            'total_shares': 0,
            'unique_authors': 0,
            'accounts': user_accounts.count()
        }
        
        return Response({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"❌ Error getting Prabowo stats: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def system_status(request):
    """
    Get system status for the enhanced TikTok actor
    """
    try:
        # Check if simple login components are available
        try:
            from actor_tiktok.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
            from actor_tiktok.utils.production_tiktok_scraper import ProductionTikTokScraper
            simple_login_available = True
        except ImportError:
            simple_login_available = False
        
        # Get user's accounts
        user_accounts = TikTokUserAccount.objects.filter(user=request.user)
        active_accounts = user_accounts.filter(is_active=True)
        
        return Response({
            'success': True,
            'status': {
                'simple_login_available': simple_login_available,
                'total_accounts': user_accounts.count(),
                'active_accounts': active_accounts.count(),
                'prabowo_scraper_ready': simple_login_available,
                'system_healthy': True
            }
        })
        
    except Exception as e:
        logger.error(f"❌ Error getting system status: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
