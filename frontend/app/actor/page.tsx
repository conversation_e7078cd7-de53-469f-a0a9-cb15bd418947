'use client';

import { useEffect, useState } from "react";
import { HealthStatus } from "@/components/actor/HealthStatus";
import { AccountList } from "@/components/actor/AccountList";
import { TaskList } from "@/components/actor/TaskList";
import { SessionMonitor } from "@/components/actor/SessionMonitor";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { getHealthStatus, getAccounts, getTasks, getTaskStatistics } from "@/lib/api/actor";
import type { HealthStatus as HealthStatusType, TikTokAccount, ActorTask, TaskMetrics } from "@/lib/types/actor";

export default function ActorDashboard() {
    const [health, setHealth] = useState<HealthStatusType | null>(null);
    const [accounts, setAccounts] = useState<TikTokAccount[]>([]);
    const [tasks, setTasks] = useState<ActorTask[]>([]);
    const [metrics, setMetrics] = useState<TaskMetrics | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);

    const fetchData = async () => {
        try {
            setLoading(true);
            setError(null);

            const [healthData, accountsData, tasksData, metricsData] = await Promise.all([
                getHealthStatus(),
                getAccounts(),
                getTasks(),
                getTaskStatistics()
            ]);

            setHealth(healthData);
            setAccounts(accountsData);
            setTasks(tasksData);
            setMetrics(metricsData);
        } catch (err) {
            setError('Failed to fetch actor data');
            console.error('Error fetching actor data:', err);
        } finally {
            setLoading(false);
        }
    };

    const refreshData = async () => {
        try {
            const [accountsData, tasksData, metricsData] = await Promise.all([
                getAccounts(),
                getTasks(),
                getTaskStatistics()
            ]);
            setAccounts(accountsData);
            setTasks(tasksData);
            setMetrics(metricsData);
        } catch (err) {
            console.error('Error refreshing data:', err);
        }
    };

    useEffect(() => {
        fetchData();

        // Refresh data every 30 seconds
        const interval = setInterval(fetchData, 30000);
        return () => clearInterval(interval);
    }, []);

    if (loading) {
        return (
            <div className="space-y-4 p-8">
                <Skeleton className="h-[200px] w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive" className="m-8">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="p-8 space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
                {health && <HealthStatus health={health} />}
                
                {metrics && (
                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-4">Task Statistics</h3>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="text-center">
                                <p className="text-sm text-gray-500">Success Rate</p>
                                <p className="text-xl font-semibold">
                                    {((metrics.success_rate || 0) * 100).toFixed(1)}%
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500">Average Duration</p>
                                <p className="text-xl font-semibold">
                                    {(metrics.average_duration || 0).toFixed(2)}s
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500">Total Tasks</p>
                                <p className="text-xl font-semibold">{metrics.total_tasks || 0}</p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500">Active Accounts</p>
                                <p className="text-xl font-semibold">{metrics.active_accounts || 0}</p>
                            </div>
                        </div>
                    </Card>
                )}
            </div>

            <div className="grid gap-6 lg:grid-cols-2">
                <div className="bg-white rounded-lg shadow">
                    <div className="p-6">
                        <h2 className="text-xl font-semibold mb-4">TikTok Accounts</h2>
                        <AccountList accounts={accounts} onAccountsChange={refreshData} />
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow">
                    <div className="p-6">
                        <h2 className="text-xl font-semibold mb-4">Session Monitor</h2>
                        <SessionMonitor />
                    </div>
                </div>
            </div>

            <div className="bg-white rounded-lg shadow">
                <div className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Recent Tasks</h2>
                    <TaskList tasks={tasks} onTasksChange={refreshData} />
                </div>
            </div>
        </div>
    );
}