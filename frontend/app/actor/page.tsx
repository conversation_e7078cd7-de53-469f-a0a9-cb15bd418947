'use client';

import { useEffect, useState } from "react";
import { HealthStatus } from "@/components/actor/HealthStatus";
import { AccountList } from "@/components/actor/AccountList";
import { TaskList } from "@/components/actor/TaskList";
import { SessionMonitor } from "@/components/actor/SessionMonitor";
import { TikTokLoginForm } from "@/components/actor/TikTokLoginForm";
import { DynamicContentSearch } from "@/components/actor/DynamicContentSearch";
import { ContentDashboard } from "@/components/actor/ContentDashboard";
import { TaskManager } from "@/components/actor/TaskManager";
import { SessionManager } from "@/components/actor/SessionManager";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, AlertCircle, TrendingUp, Users } from "lucide-react";
import { getHealthStatus, getAccounts, getTasks, getTaskStatistics } from "@/lib/api/actor";
import type { HealthStatus as HealthStatusType, TikTokAccount, ActorTask, TaskMetrics } from "@/lib/types/actor";

export default function ActorDashboard() {
    const [health, setHealth] = useState<HealthStatusType | null>(null);
    const [accounts, setAccounts] = useState<TikTokAccount[]>([]);
    const [tasks, setTasks] = useState<ActorTask[]>([]);
    const [metrics, setMetrics] = useState<TaskMetrics | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);
    const [loginSuccess, setLoginSuccess] = useState<any>(null);
    const [showContentDashboard, setShowContentDashboard] = useState(false);
    const [searchResults, setSearchResults] = useState<any>(null);
    const [activeKeywords, setActiveKeywords] = useState<string[]>(['prabowo']);
    const [activeSessions, setActiveSessions] = useState<any[]>([]);

    const fetchData = async () => {
        try {
            setLoading(true);
            setError(null);

            const [healthData, accountsData, tasksData, metricsData] = await Promise.all([
                getHealthStatus(),
                getAccounts(),
                getTasks(),
                getTaskStatistics()
            ]);

            setHealth(healthData);
            setAccounts(accountsData);
            setTasks(tasksData);
            setMetrics(metricsData);
        } catch (err) {
            setError('Failed to fetch actor data');
            console.error('Error fetching actor data:', err);
        } finally {
            setLoading(false);
        }
    };

    const refreshData = async () => {
        try {
            const [accountsData, tasksData, metricsData] = await Promise.all([
                getAccounts(),
                getTasks(),
                getTaskStatistics()
            ]);
            setAccounts(accountsData);
            setTasks(tasksData);
            setMetrics(metricsData);
        } catch (err) {
            console.error('Error refreshing data:', err);
        }
    };

    const handleLoginSuccess = (result: any) => {
        setLoginSuccess(result);
        setShowContentDashboard(true);
        refreshData(); // Refresh data after successful login
    };

    const handleLoginError = (error: string) => {
        console.error('Login error:', error);
        setLoginSuccess(null);
        setShowContentDashboard(false);
    };

    const handleSearchComplete = (results: any) => {
        setSearchResults(results);
        setActiveKeywords(results.keywords || []);
        setShowContentDashboard(true);
    };

    const handleSessionChange = (sessions: any[]) => {
        setActiveSessions(sessions);
    };

    useEffect(() => {
        fetchData();

        // Refresh data every 30 seconds
        const interval = setInterval(fetchData, 30000);
        return () => clearInterval(interval);
    }, []);

    if (loading) {
        return (
            <div className="space-y-4 p-8">
                <Skeleton className="h-[200px] w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive" className="m-8">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="p-8 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">TikTok Actor Dashboard</h1>
                    <p className="text-muted-foreground">
                        Enhanced TikTok scraping with Prabowo content monitoring
                    </p>
                </div>
                {loginSuccess && (
                    <Badge variant="default" className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4" />
                        Login Active
                    </Badge>
                )}
            </div>

            {/* Main Content Tabs */}
            <Tabs defaultValue="search" className="w-full">
                <TabsList className="grid w-full grid-cols-6">
                    <TabsTrigger value="search">Content Search</TabsTrigger>
                    <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                    <TabsTrigger value="tasks">Tasks</TabsTrigger>
                    <TabsTrigger value="sessions">Sessions</TabsTrigger>
                    <TabsTrigger value="login">Login</TabsTrigger>
                    <TabsTrigger value="system">System</TabsTrigger>
                </TabsList>

                {/* Content Search Tab */}
                <TabsContent value="search" className="space-y-6">
                    {loginSuccess ? (
                        <DynamicContentSearch
                            username="grafisone"
                            password="Puyol@102410"
                            onSearchComplete={handleSearchComplete}
                        />
                    ) : (
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5" />
                                    Login Required
                                </CardTitle>
                                <CardDescription>
                                    Please login to TikTok first to start searching for content
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm text-muted-foreground">
                                    Switch to the "Login" tab to authenticate and start searching for any keyword content.
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </TabsContent>

                {/* Dashboard Tab */}
                <TabsContent value="dashboard" className="space-y-6">
                    {showContentDashboard && searchResults ? (
                        <ContentDashboard
                            videos={searchResults.videos || []}
                            stats={searchResults.stats || {
                                totalVideos: 0,
                                totalLikes: 0,
                                totalComments: 0,
                                totalShares: 0,
                                totalViews: 0,
                                uniqueAuthors: <AUTHORS>
                                topKeywords: [],
                                topHashtags: [],
                                categoryBreakdown: {}
                            }}
                            keywords={activeKeywords}
                            loading={false}
                            onRefresh={() => {
                                // Trigger search refresh
                                console.log('Refresh dashboard');
                            }}
                            onExport={() => {
                                // Export functionality
                                console.log('Export data');
                            }}
                        />
                    ) : (
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5" />
                                    No Search Results
                                </CardTitle>
                                <CardDescription>
                                    Perform a search first to see the content dashboard
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm text-muted-foreground">
                                    Switch to the "Content Search" tab to search for keywords and view results here.
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </TabsContent>

                {/* Tasks Tab */}
                <TabsContent value="tasks" className="space-y-6">
                    {loginSuccess ? (
                        <TaskManager
                            username="grafisone"
                            password="Puyol@102410"
                            onTaskComplete={(task) => {
                                console.log('Task completed:', task);
                                // Handle task completion
                            }}
                        />
                    ) : (
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5" />
                                    Login Required
                                </CardTitle>
                                <CardDescription>
                                    Please login to TikTok first to manage tasks
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm text-muted-foreground">
                                    Tasks allow you to schedule and monitor scraping jobs.
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </TabsContent>

                {/* Sessions Tab */}
                <TabsContent value="sessions" className="space-y-6">
                    <SessionManager
                        username="grafisone"
                        password="Puyol@102410"
                        onSessionChange={handleSessionChange}
                    />
                </TabsContent>

                {/* Login Tab */}
                <TabsContent value="login" className="space-y-6">
                    <div className="max-w-2xl mx-auto">
                        <TikTokLoginForm
                            onLoginSuccess={handleLoginSuccess}
                            onLoginError={handleLoginError}
                            showTestMode={true}
                        />

                        {loginSuccess && (
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-green-600">
                                        <CheckCircle className="h-5 w-5" />
                                        Login Successful!
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2 text-sm">
                                        <p><strong>Status:</strong> ✅ Connected to TikTok</p>
                                        <p><strong>Session:</strong> {loginSuccess.cookies_count || 0} cookies active</p>
                                        <p><strong>Active Sessions:</strong> {activeSessions.filter(s => s.status === 'active').length}</p>
                                        <p><strong>Ready for:</strong> Dynamic content search and monitoring</p>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </TabsContent>

                {/* System Overview Tab */}
                <TabsContent value="system" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        {health && <HealthStatus health={health} />}

                        {metrics && (
                            <Card className="p-6">
                                <h3 className="text-lg font-semibold mb-4">Task Statistics</h3>
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="text-center">
                                        <p className="text-sm text-gray-500">Success Rate</p>
                                        <p className="text-xl font-semibold">
                                            {((metrics.success_rate || 0) * 100).toFixed(1)}%
                                        </p>
                                    </div>
                                    <div className="text-center">
                                        <p className="text-sm text-gray-500">Average Duration</p>
                                        <p className="text-xl font-semibold">
                                            {(metrics.average_completion_time || 0).toFixed(2)}s
                                        </p>
                                    </div>
                                    <div className="text-center">
                                        <p className="text-sm text-gray-500">Total Tasks</p>
                                        <p className="text-xl font-semibold">{metrics.total_tasks || 0}</p>
                                    </div>
                                    <div className="text-center">
                                        <p className="text-sm text-gray-500">Active Accounts</p>
                                        <p className="text-xl font-semibold">{metrics.active_accounts || 0}</p>
                                    </div>
                                </div>
                            </Card>
                        )}
                    </div>
                </TabsContent>

                {/* Management Tab */}
                <TabsContent value="management" className="space-y-6">
                    <div className="grid gap-6 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Users className="h-5 w-5" />
                                    TikTok Accounts
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <AccountList accounts={accounts} onAccountsChange={refreshData} />
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5" />
                                    Session Monitor
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <SessionMonitor />
                            </CardContent>
                        </Card>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Tasks</CardTitle>
                            <CardDescription>
                                Monitor and manage your TikTok scraping tasks
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <TaskList tasks={tasks} onTasksChange={refreshData} />
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
}