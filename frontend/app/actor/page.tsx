'use client';

import { useEffect, useState } from "react";
import { HealthStatus } from "@/components/actor/HealthStatus";
import { AccountList } from "@/components/actor/AccountList";
import { TaskList } from "@/components/actor/TaskList";
import { SessionMonitor } from "@/components/actor/SessionMonitor";
import { TikTokLoginForm } from "@/components/actor/TikTokLoginForm";
import { PrabowoContentDashboard } from "@/components/actor/PrabowoContentDashboard";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, AlertCircle, TrendingUp, Users } from "lucide-react";
import { getHealthStatus, getAccounts, getTasks, getTaskStatistics } from "@/lib/api/actor";
import type { HealthStatus as HealthStatusType, TikTokAccount, ActorTask, TaskMetrics } from "@/lib/types/actor";

export default function ActorDashboard() {
    const [health, setHealth] = useState<HealthStatusType | null>(null);
    const [accounts, setAccounts] = useState<TikTokAccount[]>([]);
    const [tasks, setTasks] = useState<ActorTask[]>([]);
    const [metrics, setMetrics] = useState<TaskMetrics | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);
    const [loginSuccess, setLoginSuccess] = useState<any>(null);
    const [showPrabowoDashboard, setShowPrabowoDashboard] = useState(false);

    const fetchData = async () => {
        try {
            setLoading(true);
            setError(null);

            const [healthData, accountsData, tasksData, metricsData] = await Promise.all([
                getHealthStatus(),
                getAccounts(),
                getTasks(),
                getTaskStatistics()
            ]);

            setHealth(healthData);
            setAccounts(accountsData);
            setTasks(tasksData);
            setMetrics(metricsData);
        } catch (err) {
            setError('Failed to fetch actor data');
            console.error('Error fetching actor data:', err);
        } finally {
            setLoading(false);
        }
    };

    const refreshData = async () => {
        try {
            const [accountsData, tasksData, metricsData] = await Promise.all([
                getAccounts(),
                getTasks(),
                getTaskStatistics()
            ]);
            setAccounts(accountsData);
            setTasks(tasksData);
            setMetrics(metricsData);
        } catch (err) {
            console.error('Error refreshing data:', err);
        }
    };

    const handleLoginSuccess = (result: any) => {
        setLoginSuccess(result);
        setShowPrabowoDashboard(true);
        refreshData(); // Refresh data after successful login
    };

    const handleLoginError = (error: string) => {
        console.error('Login error:', error);
        setLoginSuccess(null);
        setShowPrabowoDashboard(false);
    };

    useEffect(() => {
        fetchData();

        // Refresh data every 30 seconds
        const interval = setInterval(fetchData, 30000);
        return () => clearInterval(interval);
    }, []);

    if (loading) {
        return (
            <div className="space-y-4 p-8">
                <Skeleton className="h-[200px] w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive" className="m-8">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="p-8 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">TikTok Actor Dashboard</h1>
                    <p className="text-muted-foreground">
                        Enhanced TikTok scraping with Prabowo content monitoring
                    </p>
                </div>
                {loginSuccess && (
                    <Badge variant="default" className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4" />
                        Login Active
                    </Badge>
                )}
            </div>

            {/* Main Content Tabs */}
            <Tabs defaultValue="prabowo" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="prabowo">Prabowo Content</TabsTrigger>
                    <TabsTrigger value="login">TikTok Login</TabsTrigger>
                    <TabsTrigger value="overview">System Overview</TabsTrigger>
                    <TabsTrigger value="management">Management</TabsTrigger>
                </TabsList>

                {/* Prabowo Content Tab */}
                <TabsContent value="prabowo" className="space-y-6">
                    {showPrabowoDashboard && loginSuccess ? (
                        <PrabowoContentDashboard
                            username="grafisone"
                            password="Puyol@102410"
                            autoRefresh={true}
                            refreshInterval={300000} // 5 minutes
                        />
                    ) : (
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5" />
                                    Login Required
                                </CardTitle>
                                <CardDescription>
                                    Please login to TikTok first to access Prabowo content monitoring
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm text-muted-foreground">
                                    Switch to the "TikTok Login" tab to authenticate and start monitoring Prabowo content.
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </TabsContent>

                {/* Login Tab */}
                <TabsContent value="login" className="space-y-6">
                    <div className="max-w-2xl mx-auto">
                        <TikTokLoginForm
                            onLoginSuccess={handleLoginSuccess}
                            onLoginError={handleLoginError}
                            showTestMode={true}
                        />

                        {loginSuccess && (
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-green-600">
                                        <CheckCircle className="h-5 w-5" />
                                        Login Successful!
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2 text-sm">
                                        <p><strong>Status:</strong> ✅ Connected to TikTok</p>
                                        <p><strong>Session:</strong> {loginSuccess.cookies_count || 0} cookies active</p>
                                        <p><strong>Content Found:</strong> {loginSuccess.videos_found || 0} Prabowo videos</p>
                                        <p><strong>Ready for:</strong> Continuous Prabowo content monitoring</p>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </TabsContent>

                {/* System Overview Tab */}
                <TabsContent value="overview" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        {health && <HealthStatus health={health} />}

                        {metrics && (
                            <Card className="p-6">
                                <h3 className="text-lg font-semibold mb-4">Task Statistics</h3>
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="text-center">
                                        <p className="text-sm text-gray-500">Success Rate</p>
                                        <p className="text-xl font-semibold">
                                            {((metrics.success_rate || 0) * 100).toFixed(1)}%
                                        </p>
                                    </div>
                                    <div className="text-center">
                                        <p className="text-sm text-gray-500">Average Duration</p>
                                        <p className="text-xl font-semibold">
                                            {(metrics.average_completion_time || 0).toFixed(2)}s
                                        </p>
                                    </div>
                                    <div className="text-center">
                                        <p className="text-sm text-gray-500">Total Tasks</p>
                                        <p className="text-xl font-semibold">{metrics.total_tasks || 0}</p>
                                    </div>
                                    <div className="text-center">
                                        <p className="text-sm text-gray-500">Active Accounts</p>
                                        <p className="text-xl font-semibold">{metrics.active_accounts || 0}</p>
                                    </div>
                                </div>
                            </Card>
                        )}
                    </div>
                </TabsContent>

                {/* Management Tab */}
                <TabsContent value="management" className="space-y-6">
                    <div className="grid gap-6 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Users className="h-5 w-5" />
                                    TikTok Accounts
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <AccountList accounts={accounts} onAccountsChange={refreshData} />
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5" />
                                    Session Monitor
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <SessionMonitor />
                            </CardContent>
                        </Card>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Tasks</CardTitle>
                            <CardDescription>
                                Monitor and manage your TikTok scraping tasks
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <TaskList tasks={tasks} onTasksChange={refreshData} />
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
}