'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Play, 
    Heart, 
    MessageCircle, 
    Share, 
    ExternalLink, 
    RefreshCw, 
    TrendingUp,
    Users,
    Video,
    BarChart3,
    Filter,
    Download,
    Eye,
    Calendar,
    Tag,
    Search
} from 'lucide-react';

interface ContentVideo {
    video_id: string;
    url: string;
    author: string;
    description?: string;
    metrics: {
        likes?: string;
        comments?: string;
        shares?: string;
        views?: string;
    };
    extracted_at: string;
    keyword: string;
    category?: string;
    hashtags?: string[];
    duration?: number;
    thumbnail?: string;
}

interface ContentStats {
    totalVideos: number;
    totalLikes: number;
    totalComments: number;
    totalShares: number;
    totalViews: number;
    uniqueAuthors: <AUTHORS>
    topKeywords: string[];
    topHashtags: string[];
    categoryBreakdown: Record<string, number>;
}

interface ContentDashboardProps {
    videos: ContentVideo[];
    stats: ContentStats;
    keywords: string[];
    loading?: boolean;
    onRefresh?: () => void;
    onExport?: () => void;
}

export function ContentDashboard({ 
    videos, 
    stats, 
    keywords, 
    loading = false, 
    onRefresh, 
    onExport 
}: ContentDashboardProps) {
    const [filteredVideos, setFilteredVideos] = useState<ContentVideo[]>(videos);
    const [sortBy, setSortBy] = useState<string>('likes');
    const [filterBy, setFilterBy] = useState<string>('all');
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [selectedCategory, setSelectedCategory] = useState<string>('all');

    useEffect(() => {
        setFilteredVideos(videos);
    }, [videos]);

    useEffect(() => {
        applyFiltersAndSort();
    }, [videos, sortBy, filterBy, searchTerm, selectedCategory]);

    const applyFiltersAndSort = () => {
        let filtered = [...videos];

        // Apply search filter
        if (searchTerm) {
            filtered = filtered.filter(video => 
                video.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                video.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                video.keyword.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Apply category filter
        if (selectedCategory !== 'all') {
            filtered = filtered.filter(video => video.category === selectedCategory);
        }

        // Apply content filter
        if (filterBy !== 'all') {
            switch (filterBy) {
                case 'high-engagement':
                    filtered = filtered.filter(video => parseMetric(video.metrics.likes || '0') > 10000);
                    break;
                case 'recent':
                    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                    filtered = filtered.filter(video => new Date(video.extracted_at) > oneDayAgo);
                    break;
                case 'verified':
                    filtered = filtered.filter(video => isVerifiedAccount(video.author));
                    break;
            }
        }

        // Apply sorting
        filtered.sort((a, b) => {
            switch (sortBy) {
                case 'likes':
                    return parseMetric(b.metrics.likes || '0') - parseMetric(a.metrics.likes || '0');
                case 'comments':
                    return parseMetric(b.metrics.comments || '0') - parseMetric(a.metrics.comments || '0');
                case 'shares':
                    return parseMetric(b.metrics.shares || '0') - parseMetric(a.metrics.shares || '0');
                case 'recent':
                    return new Date(b.extracted_at).getTime() - new Date(a.extracted_at).getTime();
                case 'author':
                    return a.author.localeCompare(b.author);
                default:
                    return 0;
            }
        });

        setFilteredVideos(filtered);
    };

    const parseMetric = (metric: string): number => {
        if (!metric) return 0;
        const cleanMetric = metric.replace(/[^\d.KMB]/g, '');
        if (cleanMetric.includes('K')) {
            return Math.floor(parseFloat(cleanMetric.replace('K', '')) * 1000);
        } else if (cleanMetric.includes('M')) {
            return Math.floor(parseFloat(cleanMetric.replace('M', '')) * 1000000);
        } else if (cleanMetric.includes('B')) {
            return Math.floor(parseMetric.replace('B', '')) * **********);
        }
        return parseInt(cleanMetric) || 0;
    };

    const formatNumber = (num: number): string => {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    };

    const isVerifiedAccount = (author: string): boolean => {
        const verifiedKeywords = ['bbc', 'tvri', 'kompas', 'detik', 'republika', 'official'];
        return verifiedKeywords.some(keyword => author.toLowerCase().includes(keyword));
    };

    const getCategoryBadgeVariant = (category: string) => {
        switch (category) {
            case 'news': return 'default';
            case 'official': return 'secondary';
            case 'politics': return 'destructive';
            case 'viral': return 'outline';
            default: return 'outline';
        }
    };

    const getCategoryIcon = (category: string) => {
        switch (category) {
            case 'news': return '📰';
            case 'official': return '✅';
            case 'politics': return '🏛️';
            case 'viral': return '🔥';
            default: return '📱';
        }
    };

    const getEngagementScore = (video: ContentVideo): number => {
        const likes = parseMetric(video.metrics.likes || '0');
        const comments = parseMetric(video.metrics.comments || '0');
        const shares = parseMetric(video.metrics.shares || '0');
        return likes + (comments * 5) + (shares * 10); // Weighted engagement score
    };

    const categories = Array.from(new Set(videos.map(v => v.category).filter(Boolean)));

    return (
        <div className="space-y-6">
            {/* Header with Stats */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Content Dashboard</h2>
                    <p className="text-muted-foreground">
                        Monitoring {keywords.join(', ')} • {stats.totalVideos} videos found
                    </p>
                </div>
                <div className="flex gap-2">
                    {onExport && (
                        <Button variant="outline" onClick={onExport}>
                            <Download className="h-4 w-4 mr-2" />
                            Export
                        </Button>
                    )}
                    {onRefresh && (
                        <Button variant="outline" onClick={onRefresh} disabled={loading}>
                            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                            Refresh
                        </Button>
                    )}
                </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <Video className="h-8 w-8 text-blue-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Total Videos</p>
                                <p className="text-2xl font-bold">{stats.totalVideos}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <Heart className="h-8 w-8 text-red-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Total Likes</p>
                                <p className="text-2xl font-bold">{formatNumber(stats.totalLikes)}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <MessageCircle className="h-8 w-8 text-green-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Total Comments</p>
                                <p className="text-2xl font-bold">{formatNumber(stats.totalComments)}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <Share className="h-8 w-8 text-purple-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Total Shares</p>
                                <p className="text-2xl font-bold">{formatNumber(stats.totalShares)}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <Users className="h-8 w-8 text-orange-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Unique Authors</p>
                                <p className="text-2xl font-bold">{stats.uniqueAuthors}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Filters and Controls */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Filter className="h-5 w-5" />
                        Filters & Search
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <Input
                                placeholder="Search videos..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full"
                            />
                        </div>
                        <div>
                            <Select value={sortBy} onValueChange={setSortBy}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Sort by" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="likes">Most Liked</SelectItem>
                                    <SelectItem value="comments">Most Comments</SelectItem>
                                    <SelectItem value="shares">Most Shared</SelectItem>
                                    <SelectItem value="recent">Most Recent</SelectItem>
                                    <SelectItem value="author">By Author</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Select value={filterBy} onValueChange={setFilterBy}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Filter by" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Videos</SelectItem>
                                    <SelectItem value="high-engagement">High Engagement</SelectItem>
                                    <SelectItem value="recent">Recent (24h)</SelectItem>
                                    <SelectItem value="verified">Verified Only</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Category" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Categories</SelectItem>
                                    {categories.map((category) => (
                                        <SelectItem key={category} value={category}>
                                            {getCategoryIcon(category)} {category}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center">
                            Showing {filteredVideos.length} of {videos.length} videos
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Content Tabs */}
            <Tabs defaultValue="grid" className="w-full">
                <TabsList>
                    <TabsTrigger value="grid">Grid View</TabsTrigger>
                    <TabsTrigger value="list">List View</TabsTrigger>
                    <TabsTrigger value="analytics">Analytics</TabsTrigger>
                </TabsList>

                <TabsContent value="grid" className="space-y-4">
                    {loading ? (
                        <div className="text-center py-8">
                            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
                            <p>Loading content...</p>
                        </div>
                    ) : filteredVideos.length === 0 ? (
                        <div className="text-center py-8">
                            <Video className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                            <p>No videos found matching your criteria.</p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                            {filteredVideos.map((video, index) => (
                                <Card key={video.video_id || index} className="hover:shadow-lg transition-shadow">
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center justify-between">
                                            <CardTitle className="text-sm font-medium truncate">
                                                {video.author.startsWith('@') ? video.author : `@${video.author}`}
                                            </CardTitle>
                                            <div className="flex gap-1">
                                                {video.category && (
                                                    <Badge variant={getCategoryBadgeVariant(video.category)} className="text-xs">
                                                        {getCategoryIcon(video.category)} {video.category}
                                                    </Badge>
                                                )}
                                                {isVerifiedAccount(video.author) && (
                                                    <Badge variant="default" className="text-xs">
                                                        ✓
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                            <Tag className="h-3 w-3" />
                                            <span>{video.keyword}</span>
                                        </div>
                                        {video.description && (
                                            <CardDescription className="text-xs line-clamp-2">
                                                {video.description}
                                            </CardDescription>
                                        )}
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
                                            <div className="flex items-center gap-3">
                                                {video.metrics.likes && (
                                                    <div className="flex items-center gap-1">
                                                        <Heart className="h-3 w-3" />
                                                        <span>{video.metrics.likes}</span>
                                                    </div>
                                                )}
                                                {video.metrics.comments && (
                                                    <div className="flex items-center gap-1">
                                                        <MessageCircle className="h-3 w-3" />
                                                        <span>{video.metrics.comments}</span>
                                                    </div>
                                                )}
                                                {video.metrics.shares && (
                                                    <div className="flex items-center gap-1">
                                                        <Share className="h-3 w-3" />
                                                        <span>{video.metrics.shares}</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex gap-2">
                                            <Button 
                                                variant="outline" 
                                                size="sm" 
                                                className="flex-1"
                                                onClick={() => window.open(video.url, '_blank')}
                                            >
                                                <ExternalLink className="h-3 w-3 mr-2" />
                                                View
                                            </Button>
                                            <Button 
                                                variant="ghost" 
                                                size="sm"
                                                className="px-2"
                                            >
                                                <Eye className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    )}
                </TabsContent>

                <TabsContent value="list" className="space-y-4">
                    <div className="space-y-2">
                        {filteredVideos.map((video, index) => (
                            <Card key={video.video_id || index}>
                                <CardContent className="p-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="font-medium">{video.author}</span>
                                                {video.category && (
                                                    <Badge variant={getCategoryBadgeVariant(video.category)} className="text-xs">
                                                        {getCategoryIcon(video.category)} {video.category}
                                                    </Badge>
                                                )}
                                                <Badge variant="outline" className="text-xs">
                                                    {video.keyword}
                                                </Badge>
                                            </div>
                                            <p className="text-sm text-muted-foreground line-clamp-1">
                                                {video.description || 'No description'}
                                            </p>
                                            <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                                                <span>❤️ {video.metrics.likes}</span>
                                                <span>💬 {video.metrics.comments}</span>
                                                <span>🔄 {video.metrics.shares}</span>
                                                <span>📅 {new Date(video.extracted_at).toLocaleDateString()}</span>
                                            </div>
                                        </div>
                                        <Button 
                                            variant="outline" 
                                            size="sm"
                                            onClick={() => window.open(video.url, '_blank')}
                                        >
                                            <ExternalLink className="h-3 w-3 mr-2" />
                                            View
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </TabsContent>

                <TabsContent value="analytics" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5" />
                                    Top Keywords
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {stats.topKeywords.map((keyword, index) => (
                                        <div key={keyword} className="flex justify-between items-center">
                                            <span>#{index + 1} {keyword}</span>
                                            <Badge variant="outline">
                                                {videos.filter(v => v.keyword === keyword).length} videos
                                            </Badge>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <BarChart3 className="h-5 w-5" />
                                    Category Breakdown
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {Object.entries(stats.categoryBreakdown || {}).map(([category, count]) => (
                                        <div key={category} className="flex justify-between items-center">
                                            <span>{getCategoryIcon(category)} {category}</span>
                                            <Badge variant="outline">{count} videos</Badge>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
}
