'use client';

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Activity, AlertTriangle, CheckCircle, XCircle } from "lucide-react";
import type { HealthStatus as HealthStatusType } from "@/lib/types/actor";

interface HealthStatusProps {
    health: HealthStatusType;
}

export function HealthStatus({ health }: HealthStatusProps) {
    const getStatusIcon = () => {
        switch (health.status) {
            case 'healthy':
                return <CheckCircle className="h-5 w-5 text-green-500" />;
            case 'warning':
                return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
            case 'critical':
                return <XCircle className="h-5 w-5 text-red-500" />;
            default:
                return <Activity className="h-5 w-5 text-gray-500" />;
        }
    };

    const getStatusColor = () => {
        switch (health.status) {
            case 'healthy':
                return 'bg-green-100 text-green-800';
            case 'warning':
                return 'bg-yellow-100 text-yellow-800';
            case 'critical':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">System Health</h3>
                <div className="flex items-center gap-2">
                    {getStatusIcon()}
                    <Badge className={getStatusColor()}>
                        {health.status.toUpperCase()}
                    </Badge>
                </div>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">{health.message}</p>
            
            <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                    <p className="text-sm text-gray-500">Success Rate</p>
                    <p className="text-xl font-semibold">
                        {(health.metrics.success_rate * 100).toFixed(1)}%
                    </p>
                </div>
                <div className="text-center">
                    <p className="text-sm text-gray-500">Error Rate</p>
                    <p className="text-xl font-semibold">
                        {(health.metrics.error_rate * 100).toFixed(1)}%
                    </p>
                </div>
                <div className="text-center">
                    <p className="text-sm text-gray-500">Avg Response</p>
                    <p className="text-xl font-semibold">
                        {health.metrics.average_response_time.toFixed(0)}ms
                    </p>
                </div>
                <div className="text-center">
                    <p className="text-sm text-gray-500">Active Sessions</p>
                    <p className="text-xl font-semibold">
                        {health.metrics.active_sessions}
                    </p>
                </div>
            </div>
            
            <div className="mt-4 pt-4 border-t">
                <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Healthy Accounts</span>
                    <span className="font-semibold">{health.metrics.healthy_accounts}</span>
                </div>
            </div>
        </Card>
    );
}