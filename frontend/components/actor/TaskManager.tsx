'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
    Play, 
    Pause, 
    Square, 
    RefreshCw, 
    Clock, 
    CheckCircle, 
    AlertCircle, 
    XCircle,
    Activity,
    Calendar,
    User,
    Settings,
    Download,
    Eye,
    Trash2
} from 'lucide-react';
import { getTasks, createTask, updateTask, deleteTask, getTaskStatistics } from '@/lib/api/actor';

interface Task {
    id: string;
    task_type: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    created_at: string;
    updated_at: string;
    completed_at?: string;
    parameters: {
        keywords?: string[];
        username?: string;
        max_videos?: number;
        filters?: any;
    };
    results?: {
        videos_found?: number;
        success_rate?: number;
        error_message?: string;
    };
    estimated_duration?: number;
    actual_duration?: number;
}

interface TaskStatistics {
    total_tasks: number;
    running_tasks: number;
    completed_tasks: number;
    failed_tasks: number;
    success_rate: number;
    average_completion_time: number;
    active_accounts: number;
}

interface TaskManagerProps {
    username: string;
    password: string;
    onTaskComplete?: (task: Task) => void;
}

export function TaskManager({ username, password, onTaskComplete }: TaskManagerProps) {
    const [tasks, setTasks] = useState<Task[]>([]);
    const [statistics, setStatistics] = useState<TaskStatistics | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [selectedTask, setSelectedTask] = useState<Task | null>(null);

    useEffect(() => {
        loadTasks();
        loadStatistics();
        
        // Set up polling for task updates
        const interval = setInterval(() => {
            loadTasks();
            loadStatistics();
        }, 5000); // Poll every 5 seconds

        return () => clearInterval(interval);
    }, []);

    const loadTasks = async () => {
        try {
            const tasksData = await getTasks();
            setTasks(tasksData);
        } catch (err: any) {
            console.error('Failed to load tasks:', err);
            setError('Failed to load tasks');
        }
    };

    const loadStatistics = async () => {
        try {
            const statsData = await getTaskStatistics();
            setStatistics(statsData);
        } catch (err: any) {
            console.error('Failed to load statistics:', err);
        }
    };

    const createSearchTask = async (keywords: string[], filters: any = {}) => {
        setLoading(true);
        setError(null);

        try {
            const taskData = {
                task_type: 'content_search',
                parameters: {
                    keywords,
                    username,
                    max_videos: filters.max_videos || 20,
                    filters
                }
            };

            const newTask = await createTask(taskData);
            setTasks([newTask, ...tasks]);
            
            return newTask;
        } catch (err: any) {
            console.error('Failed to create task:', err);
            setError(err.response?.data?.error || 'Failed to create task');
            return null;
        } finally {
            setLoading(false);
        }
    };

    const cancelTask = async (taskId: string) => {
        try {
            await updateTask(taskId, { status: 'cancelled' });
            await loadTasks();
        } catch (err: any) {
            console.error('Failed to cancel task:', err);
            setError('Failed to cancel task');
        }
    };

    const retryTask = async (taskId: string) => {
        try {
            await updateTask(taskId, { status: 'pending' });
            await loadTasks();
        } catch (err: any) {
            console.error('Failed to retry task:', err);
            setError('Failed to retry task');
        }
    };

    const removeTask = async (taskId: string) => {
        try {
            await deleteTask(taskId);
            await loadTasks();
        } catch (err: any) {
            console.error('Failed to delete task:', err);
            setError('Failed to delete task');
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-4 w-4 text-yellow-500" />;
            case 'running':
                return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
            case 'completed':
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case 'failed':
                return <XCircle className="h-4 w-4 text-red-500" />;
            case 'cancelled':
                return <AlertCircle className="h-4 w-4 text-gray-500" />;
            default:
                return <Clock className="h-4 w-4" />;
        }
    };

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'pending':
                return 'secondary';
            case 'running':
                return 'default';
            case 'completed':
                return 'default';
            case 'failed':
                return 'destructive';
            case 'cancelled':
                return 'outline';
            default:
                return 'outline';
        }
    };

    const formatDuration = (seconds: number): string => {
        if (seconds < 60) return `${seconds}s`;
        if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
        return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
    };

    const getTaskTypeDisplay = (taskType: string): string => {
        switch (taskType) {
            case 'content_search':
                return 'Content Search';
            case 'login':
                return 'TikTok Login';
            case 'scrape_profile':
                return 'Profile Scraping';
            case 'scrape_hashtag':
                return 'Hashtag Scraping';
            default:
                return taskType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        }
    };

    const runningTasks = tasks.filter(t => t.status === 'running');
    const completedTasks = tasks.filter(t => t.status === 'completed');
    const failedTasks = tasks.filter(t => t.status === 'failed');

    return (
        <div className="space-y-6">
            {/* Statistics Cards */}
            {statistics && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Activity className="h-8 w-8 text-blue-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-muted-foreground">Running Tasks</p>
                                    <p className="text-2xl font-bold">{statistics.running_tasks}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <CheckCircle className="h-8 w-8 text-green-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-muted-foreground">Completed</p>
                                    <p className="text-2xl font-bold">{statistics.completed_tasks}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <XCircle className="h-8 w-8 text-red-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-muted-foreground">Failed</p>
                                    <p className="text-2xl font-bold">{statistics.failed_tasks}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <TrendingUp className="h-8 w-8 text-purple-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                                    <p className="text-2xl font-bold">{(statistics.success_rate * 100).toFixed(1)}%</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Error Display */}
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Task Tabs */}
            <Tabs defaultValue="active" className="w-full">
                <TabsList>
                    <TabsTrigger value="active">
                        Active Tasks ({runningTasks.length})
                    </TabsTrigger>
                    <TabsTrigger value="completed">
                        Completed ({completedTasks.length})
                    </TabsTrigger>
                    <TabsTrigger value="failed">
                        Failed ({failedTasks.length})
                    </TabsTrigger>
                    <TabsTrigger value="all">
                        All Tasks ({tasks.length})
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="active" className="space-y-4">
                    {runningTasks.length === 0 ? (
                        <Card>
                            <CardContent className="p-8 text-center">
                                <Activity className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                                <p className="text-muted-foreground">No active tasks</p>
                            </CardContent>
                        </Card>
                    ) : (
                        <div className="space-y-4">
                            {runningTasks.map((task) => (
                                <Card key={task.id}>
                                    <CardHeader>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                {getStatusIcon(task.status)}
                                                <CardTitle className="text-lg">
                                                    {getTaskTypeDisplay(task.task_type)}
                                                </CardTitle>
                                                <Badge variant={getStatusBadgeVariant(task.status)}>
                                                    {task.status}
                                                </Badge>
                                            </div>
                                            <div className="flex gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => cancelTask(task.id)}
                                                >
                                                    <Square className="h-4 w-4 mr-2" />
                                                    Cancel
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => setSelectedTask(task)}
                                                >
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                        <CardDescription>
                                            Keywords: {task.parameters.keywords?.join(', ') || 'N/A'}
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-2">
                                            <div className="flex justify-between text-sm">
                                                <span>Progress</span>
                                                <span>{task.progress}%</span>
                                            </div>
                                            <Progress value={task.progress} className="w-full" />
                                            <div className="flex justify-between text-xs text-muted-foreground">
                                                <span>Started: {new Date(task.created_at).toLocaleString()}</span>
                                                {task.estimated_duration && (
                                                    <span>Est. Duration: {formatDuration(task.estimated_duration)}</span>
                                                )}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    )}
                </TabsContent>

                <TabsContent value="completed" className="space-y-4">
                    {completedTasks.length === 0 ? (
                        <Card>
                            <CardContent className="p-8 text-center">
                                <CheckCircle className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                                <p className="text-muted-foreground">No completed tasks</p>
                            </CardContent>
                        </Card>
                    ) : (
                        <div className="space-y-4">
                            {completedTasks.map((task) => (
                                <Card key={task.id}>
                                    <CardHeader>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                {getStatusIcon(task.status)}
                                                <CardTitle className="text-lg">
                                                    {getTaskTypeDisplay(task.task_type)}
                                                </CardTitle>
                                                <Badge variant={getStatusBadgeVariant(task.status)}>
                                                    {task.status}
                                                </Badge>
                                            </div>
                                            <div className="flex gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => setSelectedTask(task)}
                                                >
                                                    <Eye className="h-4 w-4 mr-2" />
                                                    View Results
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => removeTask(task.id)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                        <CardDescription>
                                            Keywords: {task.parameters.keywords?.join(', ') || 'N/A'}
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                            <div>
                                                <p className="text-muted-foreground">Videos Found</p>
                                                <p className="font-medium">{task.results?.videos_found || 0}</p>
                                            </div>
                                            <div>
                                                <p className="text-muted-foreground">Duration</p>
                                                <p className="font-medium">
                                                    {task.actual_duration ? formatDuration(task.actual_duration) : 'N/A'}
                                                </p>
                                            </div>
                                            <div>
                                                <p className="text-muted-foreground">Completed</p>
                                                <p className="font-medium">
                                                    {task.completed_at ? new Date(task.completed_at).toLocaleString() : 'N/A'}
                                                </p>
                                            </div>
                                            <div>
                                                <p className="text-muted-foreground">Success Rate</p>
                                                <p className="font-medium">
                                                    {task.results?.success_rate ? `${(task.results.success_rate * 100).toFixed(1)}%` : 'N/A'}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    )}
                </TabsContent>

                <TabsContent value="failed" className="space-y-4">
                    {failedTasks.length === 0 ? (
                        <Card>
                            <CardContent className="p-8 text-center">
                                <XCircle className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                                <p className="text-muted-foreground">No failed tasks</p>
                            </CardContent>
                        </Card>
                    ) : (
                        <div className="space-y-4">
                            {failedTasks.map((task) => (
                                <Card key={task.id}>
                                    <CardHeader>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                {getStatusIcon(task.status)}
                                                <CardTitle className="text-lg">
                                                    {getTaskTypeDisplay(task.task_type)}
                                                </CardTitle>
                                                <Badge variant={getStatusBadgeVariant(task.status)}>
                                                    {task.status}
                                                </Badge>
                                            </div>
                                            <div className="flex gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => retryTask(task.id)}
                                                >
                                                    <RefreshCw className="h-4 w-4 mr-2" />
                                                    Retry
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => removeTask(task.id)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                        <CardDescription>
                                            Keywords: {task.parameters.keywords?.join(', ') || 'N/A'}
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <Alert variant="destructive">
                                            <AlertCircle className="h-4 w-4" />
                                            <AlertDescription>
                                                {task.results?.error_message || 'Task failed with unknown error'}
                                            </AlertDescription>
                                        </Alert>
                                        <div className="mt-4 text-sm text-muted-foreground">
                                            Failed at: {new Date(task.updated_at).toLocaleString()}
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    )}
                </TabsContent>

                <TabsContent value="all" className="space-y-4">
                    <div className="space-y-4">
                        {tasks.map((task) => (
                            <Card key={task.id}>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            {getStatusIcon(task.status)}
                                            <CardTitle className="text-lg">
                                                {getTaskTypeDisplay(task.task_type)}
                                            </CardTitle>
                                            <Badge variant={getStatusBadgeVariant(task.status)}>
                                                {task.status}
                                            </Badge>
                                        </div>
                                        <div className="flex gap-2">
                                            {task.status === 'running' && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => cancelTask(task.id)}
                                                >
                                                    <Square className="h-4 w-4 mr-2" />
                                                    Cancel
                                                </Button>
                                            )}
                                            {task.status === 'failed' && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => retryTask(task.id)}
                                                >
                                                    <RefreshCw className="h-4 w-4 mr-2" />
                                                    Retry
                                                </Button>
                                            )}
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => setSelectedTask(task)}
                                            >
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                    <CardDescription>
                                        Keywords: {task.parameters.keywords?.join(', ') || 'N/A'} • 
                                        Created: {new Date(task.created_at).toLocaleString()}
                                    </CardDescription>
                                </CardHeader>
                            </Card>
                        ))}
                    </div>
                </TabsContent>
            </Tabs>

            {/* Task Detail Modal would go here */}
            {selectedTask && (
                <Card className="mt-6">
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <CardTitle>Task Details</CardTitle>
                            <Button variant="ghost" onClick={() => setSelectedTask(null)}>
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm font-medium">Task ID</p>
                                    <p className="text-sm text-muted-foreground">{selectedTask.id}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Type</p>
                                    <p className="text-sm text-muted-foreground">{getTaskTypeDisplay(selectedTask.task_type)}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Status</p>
                                    <Badge variant={getStatusBadgeVariant(selectedTask.status)}>
                                        {selectedTask.status}
                                    </Badge>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Progress</p>
                                    <p className="text-sm text-muted-foreground">{selectedTask.progress}%</p>
                                </div>
                            </div>
                            <div>
                                <p className="text-sm font-medium">Parameters</p>
                                <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                                    {JSON.stringify(selectedTask.parameters, null, 2)}
                                </pre>
                            </div>
                            {selectedTask.results && (
                                <div>
                                    <p className="text-sm font-medium">Results</p>
                                    <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                                        {JSON.stringify(selectedTask.results, null, 2)}
                                    </pre>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
