'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Eye, EyeOff, Shield } from 'lucide-react';
import { loginToTikTok } from '@/lib/api/actor';

interface TikTokLoginFormProps {
    onLoginSuccess?: (taskId: string) => void;
    onLoginError?: (error: string) => void;
}

export function TikTokLoginForm({ onLoginSuccess, onLoginError }: TikTokLoginFormProps) {
    const [formData, setFormData] = useState({
        tiktok_username: '',
        tiktok_password: '',
        use_2fa: false,
        two_factor_code: '',
        remember_session: true
    });
    
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [showPassword, setShowPassword] = useState(false);

    const handleInputChange = (field: string, value: string | boolean) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error when user starts typing
        if (error) {
            setError(null);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!formData.tiktok_username.trim() || !formData.tiktok_password.trim()) {
            setError('Username and password are required');
            return;
        }
        
        if (formData.use_2fa && !formData.two_factor_code.trim()) {
            setError('Two-factor authentication code is required');
            return;
        }
        
        setLoading(true);
        setError(null);
        setSuccess(null);
        
        try {
            const response = await loginToTikTok({
                tiktok_username: formData.tiktok_username.trim(),
                tiktok_password: formData.tiktok_password,
                use_2fa: formData.use_2fa,
                two_factor_code: formData.two_factor_code.trim() || undefined,
                remember_session: formData.remember_session
            });
            
            setSuccess(`${response.message} (Task ID: ${response.task_id})`);
            
            // Reset form
            setFormData({
                tiktok_username: '',
                tiktok_password: '',
                use_2fa: false,
                two_factor_code: '',
                remember_session: true
            });
            
            if (onLoginSuccess) {
                onLoginSuccess(response.task_id);
            }
        } catch (err: any) {
            console.error('Login failed:', err);
            let errorMessage = 'Login failed. Please try again.';
            
            if (err.response?.status === 400) {
                if (err.response?.data) {
                    const errorData = err.response.data;
                    if (typeof errorData === 'object') {
                        const errorMessages = Object.entries(errorData)
                            .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)
                            .join('. ');
                        errorMessage = errorMessages;
                    } else {
                        errorMessage = errorData.toString();
                    }
                } else {
                    errorMessage = 'Invalid request. Please check your credentials.';
                }
            } else if (err.response?.status === 401) {
                errorMessage = 'Authentication required. Please log in to your account first.';
            } else if (err.response?.data) {
                const errorData = err.response.data;
                if (typeof errorData === 'object') {
                    const errorMessages = Object.entries(errorData)
                        .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)
                        .join('. ');
                    errorMessage = errorMessages;
                } else {
                    errorMessage = errorData.toString();
                }
            } else {
                errorMessage = err.message || 'Login failed. Please try again.';
            }
            
            setError(errorMessage);
            
            if (onLoginError) {
                onLoginError(errorMessage);
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card className="w-full max-w-md mx-auto">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    TikTok Login
                </CardTitle>
                <CardDescription>
                    Login to your TikTok account to enable scraping functionality
                </CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="username">TikTok Username or Email</Label>
                        <Input
                            id="username"
                            type="text"
                            placeholder="@username or email"
                            value={formData.tiktok_username}
                            onChange={(e) => handleInputChange('tiktok_username', e.target.value)}
                            disabled={loading}
                            autoComplete="username"
                            required
                        />
                    </div>
                    
                    <div className="space-y-2">
                        <Label htmlFor="password">Password</Label>
                        <div className="relative">
                            <Input
                                id="password"
                                type={showPassword ? 'text' : 'password'}
                                placeholder="Enter your password"
                                value={formData.tiktok_password}
                                onChange={(e) => handleInputChange('tiktok_password', e.target.value)}
                                disabled={loading}
                                autoComplete="current-password"
                                required
                            />
                            <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() => setShowPassword(!showPassword)}
                                disabled={loading}
                            >
                                {showPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                ) : (
                                    <Eye className="h-4 w-4" />
                                )}
                            </Button>
                        </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id="use_2fa"
                            checked={formData.use_2fa}
                            onCheckedChange={(checked) => handleInputChange('use_2fa', checked as boolean)}
                            disabled={loading}
                        />
                        <Label htmlFor="use_2fa" className="text-sm">
                            Use Two-Factor Authentication
                        </Label>
                    </div>
                    
                    {formData.use_2fa && (
                        <div className="space-y-2">
                            <Label htmlFor="two_factor_code">2FA Code</Label>
                            <Input
                                id="two_factor_code"
                                type="text"
                                placeholder="Enter 6-digit code"
                                value={formData.two_factor_code}
                                onChange={(e) => handleInputChange('two_factor_code', e.target.value)}
                                disabled={loading}
                                maxLength={6}
                                pattern="[0-9]{6}"
                            />
                        </div>
                    )}
                    
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id="remember_session"
                            checked={formData.remember_session}
                            onCheckedChange={(checked) => handleInputChange('remember_session', checked as boolean)}
                            disabled={loading}
                        />
                        <Label htmlFor="remember_session" className="text-sm">
                            Remember session
                        </Label>
                    </div>
                    
                    {error && (
                        <Alert variant="destructive">
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}
                    
                    {success && (
                        <Alert>
                            <AlertDescription>{success}</AlertDescription>
                        </Alert>
                    )}
                    
                    <Button
                        type="submit"
                        className="w-full"
                        disabled={loading}
                    >
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Logging in...
                            </>
                        ) : (
                            'Login to TikTok'
                        )}
                    </Button>
                </form>
            </CardContent>
        </Card>
    );
}