import { api } from "@/lib/axios";
import type { HealthStatus, TikTokAccount, ActorTask, ActorTaskType, TaskMetrics, ScrapedData, TikTokSession } from "@/lib/types/actor";

const BASE_URL = "/actor";

// Health Check
export async function getHealthStatus(): Promise<HealthStatus> {
    const response = await api.get(`${BASE_URL}/health/`);
    return response.data;
}

// Account Management
export async function getAccounts(): Promise<TikTokAccount[]> {
    const response = await api.get(`${BASE_URL}/accounts/`);
    return response.data.results || response.data;
}

export async function getAccount(accountId: number): Promise<TikTokAccount> {
    const response = await api.get(`${BASE_URL}/accounts/${accountId}/`);
    return response.data;
}

export async function createAccount(data: {
    tiktok_username: string;
    tiktok_user_id?: string;
    email?: string;
    phone?: string;
    password?: string;
    bio?: string;
    profile_image_url?: string;
}): Promise<TikTokAccount> {
    const response = await api.post(`${BASE_URL}/accounts/`, data);
    return response.data;
}

export async function updateAccount(accountId: number, data: Partial<TikTokAccount>): Promise<TikTokAccount> {
    const response = await api.patch(`${BASE_URL}/accounts/${accountId}/`, data);
    return response.data;
}

export async function deleteAccount(accountId: number): Promise<void> {
    await api.delete(`${BASE_URL}/accounts/${accountId}/`);
}

// Task Management
export async function getTasks(): Promise<ActorTask[]> {
    const response = await api.get(`${BASE_URL}/tasks/`);
    return response.data.results || response.data;
}

export async function getTask(taskId: number): Promise<ActorTask> {
    const response = await api.get(`${BASE_URL}/tasks/${taskId}/`);
    return response.data;
}

export async function createTask(data: {
    account_id: number;
    task_type: ActorTaskType;
    target_username?: string;
    target_user_id?: string;
    max_items?: number;
    config?: any;
}): Promise<ActorTask> {
    // Convert frontend fields to backend fields
    const { account_id, target_username, target_user_id, task_type, ...rest } = data;
    
    // Use target_username if provided, otherwise target_user_id
    const target_identifier = target_username || target_user_id || undefined;
    
    // Generate task name based on task type and target
    let task_name = '';
    switch (task_type) {
        case 'MY_VIDEOS':
            task_name = 'My Videos';
            break;
        case 'MY_FOLLOWERS':
            task_name = 'My Followers';
            break;
        case 'MY_FOLLOWING':
            task_name = 'My Following';
            break;
        case 'MY_LIKES':
            task_name = 'My Liked Videos';
            break;
        case 'FEED_SCRAPE':
            task_name = 'Feed Scraping';
            break;
        case 'TARGETED_USER':
            task_name = `Targeted User: ${target_identifier || 'Unknown'}`;
            break;
        case 'HASHTAG_ANALYSIS':
            task_name = `Hashtag Analysis: ${target_identifier || 'Unknown'}`;
            break;
        case 'COMPETITOR_ANALYSIS':
            task_name = `Competitor Analysis: ${target_identifier || 'Unknown'}`;
            break;
        default:
            task_name = `${task_type} Task`;
    }
    
    const payload = { 
        tiktok_account_id: account_id,
        task_name,
        task_type,
        target_identifier,
        ...rest 
    };
    
    // Remove undefined fields
    Object.keys(payload).forEach(key => {
        if ((payload as any)[key] === undefined) {
            delete (payload as any)[key];
        }
    });
    
    const response = await api.post(`${BASE_URL}/tasks/`, payload);
    return response.data;
}

export async function updateTask(taskId: number, data: Partial<ActorTask>): Promise<ActorTask> {
    const response = await api.patch(`${BASE_URL}/tasks/${taskId}/`, data);
    return response.data;
}

export async function retryTask(taskId: number): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/tasks/${taskId}/retry/`);
    return response.data;
}

export async function cancelTask(taskId: number): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/tasks/${taskId}/cancel/`);
    return response.data;
}

export async function deleteTask(taskId: number): Promise<void> {
    await api.delete(`${BASE_URL}/tasks/${taskId}/`);
}

// Task Statistics
export async function getTaskStatistics(): Promise<TaskMetrics> {
    const response = await api.get(`${BASE_URL}/tasks/stats/`);
    return response.data;
}

// Scraped Data Operations
export async function getScrapedData(params?: {
    task_id?: number;
    data_type?: 'PROFILE' | 'VIDEO' | 'FOLLOWER' | 'FOLLOWING';
    page?: number;
    page_size?: number;
}): Promise<{ results: ScrapedData[]; count: number; next: string | null; previous: string | null }> {
    const queryParams = new URLSearchParams();
    if (params?.task_id) queryParams.append('task_id', params.task_id.toString());
    if (params?.data_type) queryParams.append('data_type', params.data_type);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());
    
    const response = await api.get(`${BASE_URL}/scraped-data/?${queryParams.toString()}`);
    return response.data;
}

export async function getScrapedDataByTask(taskId: number): Promise<{ task: ActorTask; scraped_count: number; data: any[] }> {
    const response = await api.get(`${BASE_URL}/scraped-data/by_task/?task_id=${taskId}`);
    return response.data;
}

// Session Management
export async function getSessions(): Promise<TikTokSession[]> {
    const response = await api.get(`${BASE_URL}/sessions/`);
    return response.data.results || response.data;
}

export async function getSession(sessionId: number): Promise<TikTokSession> {
    const response = await api.get(`${BASE_URL}/sessions/${sessionId}/`);
    return response.data;
}

export async function createSession(data: {
    account_id: number;
    user_agent?: string;
    proxy_used?: string;
}): Promise<TikTokSession> {
    const response = await api.post(`${BASE_URL}/sessions/`, data);
    return response.data;
}

export async function updateSession(sessionId: number, data: Partial<TikTokSession>): Promise<TikTokSession> {
    const response = await api.patch(`${BASE_URL}/sessions/${sessionId}/`, data);
    return response.data;
}

export async function deleteSession(sessionId: number): Promise<void> {
    await api.delete(`${BASE_URL}/sessions/${sessionId}/`);
}

// Session Actions
export async function markSessionUnhealthy(sessionId: number): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/sessions/${sessionId}/mark_unhealthy/`);
    return response.data;
}

export async function resetSessionMetrics(sessionId: number): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/sessions/${sessionId}/reset_metrics/`);
    return response.data;
}

// TikTok Login
export async function loginToTikTok(data: {
    tiktok_username: string;
    tiktok_password: string;
    use_2fa?: boolean;
    two_factor_code?: string;
    remember_session?: boolean;
}): Promise<{ message: string; task_id: string; status: string }> {
    const response = await api.post(`${BASE_URL}/login/`, data);
    return response.data;
}